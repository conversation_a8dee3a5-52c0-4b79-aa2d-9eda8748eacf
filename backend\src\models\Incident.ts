import mongoose, { Document, Schema } from 'mongoose';
import { IncidentType, IncidentStatus, Priority, Location, MediaFile } from '../shared/types';

export interface IIncident extends Document {
  type: IncidentType;
  title: string;
  description: string;
  location: Location;
  reportedBy: mongoose.Types.ObjectId;
  reportedAt: Date;
  status: IncidentStatus;
  priority: Priority;
  media: MediaFile[];
  tags: string[];
  isOfflineCreated: boolean;
  syncedAt?: Date;
  assignedTo?: mongoose.Types.ObjectId;
  resolvedAt?: Date;
  notes: string[];
  createdAt: Date;
  updatedAt: Date;
}

const locationSchema = new Schema<Location>({
  latitude: {
    type: Number,
    required: [true, 'Latitude is required'],
    min: [-90, 'Latitude must be between -90 and 90'],
    max: [90, 'Latitude must be between -90 and 90']
  },
  longitude: {
    type: Number,
    required: [true, 'Longitude is required'],
    min: [-180, 'Longitude must be between -180 and 180'],
    max: [180, 'Longitude must be between -180 and 180']
  },
  address: {
    type: String,
    trim: true,
    maxlength: [200, 'Address cannot exceed 200 characters']
  },
  region: {
    type: String,
    trim: true,
    maxlength: [100, 'Region cannot exceed 100 characters']
  },
  city: {
    type: String,
    trim: true,
    maxlength: [100, 'City cannot exceed 100 characters']
  },
  country: {
    type: String,
    trim: true,
    maxlength: [100, 'Country cannot exceed 100 characters']
  }
}, { _id: false });

const mediaFileSchema = new Schema<MediaFile>({
  id: {
    type: String,
    required: true
  },
  filename: {
    type: String,
    required: [true, 'Filename is required']
  },
  originalName: {
    type: String,
    required: [true, 'Original name is required']
  },
  mimeType: {
    type: String,
    required: [true, 'MIME type is required']
  },
  size: {
    type: Number,
    required: [true, 'File size is required'],
    min: [0, 'File size must be positive']
  },
  url: {
    type: String,
    required: [true, 'File URL is required']
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  }
}, { _id: false });

const incidentSchema = new Schema<IIncident>({
  type: {
    type: String,
    enum: Object.values(IncidentType),
    required: [true, 'Incident type is required']
  },
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  location: {
    type: locationSchema,
    required: [true, 'Location is required']
  },
  reportedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Reporter is required']
  },
  reportedAt: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: Object.values(IncidentStatus),
    default: IncidentStatus.PENDING
  },
  priority: {
    type: String,
    enum: Object.values(Priority),
    required: [true, 'Priority is required'],
    default: Priority.MEDIUM
  },
  media: [mediaFileSchema],
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, 'Tag cannot exceed 50 characters']
  }],
  isOfflineCreated: {
    type: Boolean,
    default: false
  },
  syncedAt: {
    type: Date
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  resolvedAt: {
    type: Date
  },
  notes: [{
    type: String,
    trim: true,
    maxlength: [500, 'Note cannot exceed 500 characters']
  }]
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc: any, ret: any) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for better query performance
incidentSchema.index({ type: 1 });
incidentSchema.index({ status: 1 });
incidentSchema.index({ priority: 1 });
incidentSchema.index({ reportedBy: 1 });
incidentSchema.index({ reportedAt: -1 });
incidentSchema.index({ 'location.region': 1 });
incidentSchema.index({ 'location.city': 1 });
incidentSchema.index({ tags: 1 });
incidentSchema.index({ isOfflineCreated: 1 });

// Compound indexes
incidentSchema.index({ status: 1, priority: 1 });
incidentSchema.index({ type: 1, reportedAt: -1 });
incidentSchema.index({ 'location.region': 1, reportedAt: -1 });

// Text index for search functionality
incidentSchema.index({
  title: 'text',
  description: 'text',
  tags: 'text',
  'location.address': 'text'
});

// Geospatial index for location-based queries
incidentSchema.index({ 'location.latitude': 1, 'location.longitude': 1 });

export const Incident = mongoose.model<IIncident>('Incident', incidentSchema);
