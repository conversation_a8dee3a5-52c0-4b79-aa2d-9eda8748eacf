# Police Reporting System - Backend API

A secure, scalable Node.js backend API for the Police Field Reporting System.

## 🚀 Features

- **Authentication & Authorization**: JWT-based auth with role-based access control
- **Incident Management**: CRUD operations for police incident reports
- **File Upload**: Secure media file handling for photos and videos
- **Real-time Statistics**: Analytics and reporting capabilities
- **Security**: Rate limiting, input validation, CORS protection
- **Database**: MongoDB with Mongoose ODM
- **TypeScript**: Full type safety and modern JavaScript features

## 📋 Prerequisites

- Node.js 18+
- MongoDB 6+
- npm or yarn

## 🛠️ Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your configuration:
   - Set `JWT_SECRET` and `JWT_REFRESH_SECRET` to secure random strings
   - Configure `MONGODB_URI` for your MongoDB instance
   - Adjust other settings as needed

3. **Build the project**
   ```bash
   npm run build
   ```

## 🚀 Running the Application

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm run build
npm start
```

The API will be available at `http://localhost:3000/api`

## 📚 API Documentation

### Authentication Endpoints

#### POST /api/auth/login
Login with email and password
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### GET /api/auth/profile
Get current user profile (requires authentication)

#### PUT /api/auth/profile
Update user profile (requires authentication)

#### POST /api/auth/register
Register new user (admin only)

### Incident Endpoints

#### GET /api/incidents
Get incidents with filtering and pagination
- Query parameters: `type`, `status`, `priority`, `region`, `dateFrom`, `dateTo`, `search`, `page`, `limit`

#### POST /api/incidents
Create new incident (requires authentication)

#### GET /api/incidents/:id
Get incident by ID

#### PUT /api/incidents/:id
Update incident

#### DELETE /api/incidents/:id
Delete incident (admin/supervisor only)

#### GET /api/incidents/stats
Get incident statistics

## 🗃️ Database Schema

### User Model
- email, password, firstName, lastName
- role (officer, supervisor, analyst, admin)
- unit, badgeNumber, isActive
- timestamps

### Incident Model
- type, title, description, location
- reportedBy, reportedAt, status, priority
- media files, tags, notes
- timestamps

## 🔐 Security Features

- JWT authentication with refresh tokens
- Password hashing with bcrypt
- Rate limiting (100 requests per 15 minutes)
- Input validation and sanitization
- CORS protection
- Helmet security headers
- File upload restrictions

## 🌍 Environment Variables

See `.env.example` for all available configuration options.

## 📁 Project Structure

```
src/
├── config/          # Configuration files
├── controllers/     # Route controllers
├── middleware/      # Custom middleware
├── models/          # Database models
├── routes/          # API routes
├── services/        # Business logic
├── utils/           # Utility functions
├── app.ts          # Express app setup
└── index.ts        # Server entry point
```

## 🧪 Testing

```bash
npm test
```

## 📦 Deployment

1. Set environment variables for production
2. Build the application: `npm run build`
3. Start the server: `npm start`

## 🤝 Contributing

1. Follow TypeScript and ESLint rules
2. Add proper error handling
3. Include input validation
4. Write tests for new features
5. Update documentation
