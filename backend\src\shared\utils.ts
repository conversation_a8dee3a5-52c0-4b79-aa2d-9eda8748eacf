// Shared utilities for the Police Reporting System

import { I18nText, IncidentType, Priority, IncidentStatus } from './types';
import { VALIDATION_RULES, COLORS } from './constants';

/**
 * Format date for display
 */
export const formatDate = (date: Date | string, locale: string = 'en'): string => {
  const d = new Date(date);
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };

  return d.toLocaleDateString(locale, options);
};

/**
 * Format relative time (e.g., "2 hours ago")
 */
export const formatRelativeTime = (date: Date | string, locale: string = 'en'): string => {
  const d = new Date(date);
  const now = new Date();
  const diffMs = now.getTime() - d.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffMins < 1) return locale === 'ar' ? 'الآن' : locale === 'fr' ? 'maintenant' : 'now';
  if (diffMins < 60) return locale === 'ar' ? `منذ ${diffMins} دقيقة` : locale === 'fr' ? `il y a ${diffMins} min` : `${diffMins}m ago`;
  if (diffHours < 24) return locale === 'ar' ? `منذ ${diffHours} ساعة` : locale === 'fr' ? `il y a ${diffHours}h` : `${diffHours}h ago`;
  return locale === 'ar' ? `منذ ${diffDays} يوم` : locale === 'fr' ? `il y a ${diffDays}j` : `${diffDays}d ago`;
};

/**
 * Get localized text
 */
export const getLocalizedText = (text: I18nText, locale: string = 'en'): string => {
  return text[locale as keyof I18nText] || text.en;
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  return VALIDATION_RULES.EMAIL_REGEX.test(email);
};

/**
 * Validate password strength
 */
export const isValidPassword = (password: string): boolean => {
  return password.length >= VALIDATION_RULES.PASSWORD_MIN_LENGTH;
};

/**
 * Get color for priority
 */
export const getPriorityColor = (priority: Priority): string => {
  return COLORS.PRIORITY[priority];
};

/**
 * Get color for status
 */
export const getStatusColor = (status: IncidentStatus): string => {
  return COLORS.STATUS[status];
};

/**
 * Get color for incident type
 */
export const getIncidentTypeColor = (type: IncidentType): string => {
  return COLORS.INCIDENT_TYPE[type];
};

/**
 * Calculate distance between two coordinates (Haversine formula)
 */
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

const toRadians = (degrees: number): number => {
  return degrees * (Math.PI / 180);
};

/**
 * Format file size
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Debounce function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Generate unique ID
 */
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * Sanitize filename
 */
export const sanitizeFilename = (filename: string): string => {
  return filename.replace(/[^a-z0-9.-]/gi, '_').toLowerCase();
};

/**
 * Check if file type is allowed
 */
export const isAllowedFileType = (mimeType: string, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(mimeType);
};

/**
 * Truncate text
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substr(0, maxLength) + '...';
};

/**
 * Deep clone object
 */
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

/**
 * Check if device is mobile (server-side version)
 */
export const isMobile = (userAgent?: string): boolean => {
  if (!userAgent) return false;
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
};

/**
 * Get language from Accept-Language header
 */
export const getLanguageFromHeader = (acceptLanguage?: string): string => {
  if (!acceptLanguage) return 'en';
  const lang = acceptLanguage.split(',')[0].split('-')[0];
  return lang || 'en';
};
