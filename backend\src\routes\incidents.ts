import { Router } from 'express';
import {
  createIncident,
  getIncidents,
  getIncidentById,
  updateIncident,
  deleteIncident,
  getIncidentStats,
  assignIncident
} from '../controllers';
import {
  authenticate,
  requireAdminOrSupervisor,
  validateIncidentCreation,
  validateIncidentUpdate,
  validateObjectId,
  validatePagination,
  validateDateRange
} from '../middleware';
import { body } from 'express-validator';

const router = Router();

// All routes require authentication
router.use(authenticate);

// GET /api/incidents/stats - Get incident statistics
router.get('/stats', validateDateRange, getIncidentStats);

// GET /api/incidents - Get all incidents with filtering
router.get('/', validatePagination, validateDateRange, getIncidents);

// POST /api/incidents - Create new incident
router.post('/', validateIncidentCreation, createIncident);

// GET /api/incidents/:id - Get incident by ID
router.get('/:id', validateObjectId('id'), getIncidentById);

// PUT /api/incidents/:id - Update incident
router.put('/:id',
  validateObjectId('id'),
  validateIncidentUpdate,
  updateIncident
);

// DELETE /api/incidents/:id - Delete incident (Admin/Supervisor only)
router.delete('/:id',
  requireAdminOrSupervisor,
  validateObjectId('id'),
  deleteIncident
);

// PUT /api/incidents/:id/assign - Assign incident to officer (Supervisor/Admin only)
router.put('/:id/assign',
  requireAdminOrSupervisor,
  validateObjectId('id'),
  body('assignedTo')
    .isMongoId()
    .withMessage('Invalid assigned officer ID'),
  assignIncident
);

export default router;
