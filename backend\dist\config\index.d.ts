interface Config {
    NODE_ENV: string;
    PORT: number;
    HOST: string;
    MONGODB_URI: string;
    MONGODB_TEST_URI: string;
    JWT_SECRET: string;
    JWT_EXPIRES_IN: string;
    JWT_REFRESH_SECRET: string;
    JWT_REFRESH_EXPIRES_IN: string;
    MAX_FILE_SIZE: number;
    UPLOAD_PATH: string;
    ALLOWED_FILE_TYPES: string[];
    RATE_LIMIT_WINDOW_MS: number;
    RATE_LIMIT_MAX_REQUESTS: number;
    CORS_ORIGIN: string[];
    BCRYPT_ROUNDS: number;
    API_VERSION: string;
    API_PREFIX: string;
}
export declare const config: Config;
export default config;
//# sourceMappingURL=index.d.ts.map