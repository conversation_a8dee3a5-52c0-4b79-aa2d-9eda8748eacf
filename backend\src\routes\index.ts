import { Router } from 'express';
import authRoutes from './auth';
import incidentsRoutes from './incidents';

const router = Router();

// Mount routes
router.use('/auth', authRoutes);
router.use('/incidents', incidentsRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Police Reporting System API is running',
    timestamp: new Date().toISOString()
  });
});

export default router;
