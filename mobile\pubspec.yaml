name: police_reporting_app
description: Police Field Reporting System - Mobile App for Officers

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter

  # UI & Navigation
  cupertino_icons: ^1.0.6
  flutter_localizations:
    sdk: flutter

  # State Management
  provider: ^6.1.1

  # HTTP & API
  http: ^1.1.2
  dio: ^5.4.0

  # Local Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0

  # Location & Maps
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  google_maps_flutter: ^2.5.0

  # Media & Files
  image_picker: ^1.0.4
  camera: ^0.10.5+5
  video_player: ^2.8.1
  path_provider: ^2.1.1

  # Connectivity & Sync
  connectivity_plus: ^5.0.2

  # Utils
  intl: ^0.20.2
  uuid: ^4.2.1

  # Security
  flutter_secure_storage: ^9.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/

  # Internationalization
  generate: true

flutter_intl:
  enabled: true
  class_name: S
  main_locale: en
  arb_dir: lib/l10n
  output_dir: lib/generated
