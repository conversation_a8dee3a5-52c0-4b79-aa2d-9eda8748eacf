import mongoose, { Document } from 'mongoose';
import { IncidentType, IncidentStatus, Priority, Location, MediaFile } from '../shared/types';
export interface IIncident extends Document {
    type: IncidentType;
    title: string;
    description: string;
    location: Location;
    reportedBy: mongoose.Types.ObjectId;
    reportedAt: Date;
    status: IncidentStatus;
    priority: Priority;
    media: MediaFile[];
    tags: string[];
    isOfflineCreated: boolean;
    syncedAt?: Date;
    assignedTo?: mongoose.Types.ObjectId;
    resolvedAt?: Date;
    notes: string[];
    createdAt: Date;
    updatedAt: Date;
}
export declare const Incident: mongoose.Model<IIncident, {}, {}, {}, mongoose.Document<unknown, {}, IIncident, {}> & IIncident & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Incident.d.ts.map