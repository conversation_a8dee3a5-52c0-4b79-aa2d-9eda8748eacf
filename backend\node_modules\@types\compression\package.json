{"name": "@types/compression", "version": "1.8.1", "description": "TypeScript definitions for compression", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/compression", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>", "url": "https://github.com/santialbo"}, {"name": "<PERSON>", "githubUsername": "rburgt", "url": "https://github.com/rburgt"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/neil<PERSON><PERSON>son"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/compression"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "6b4eb04fd4e192c872fa74993aa678fec3430d7eef528a26b337b14bec479a91", "typeScriptVersion": "5.1"}