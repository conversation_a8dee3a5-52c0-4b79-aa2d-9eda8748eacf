"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLanguageFromHeader = exports.isMobile = exports.deepClone = exports.truncateText = exports.isAllowedFileType = exports.sanitizeFilename = exports.generateId = exports.debounce = exports.formatFileSize = exports.calculateDistance = exports.getIncidentTypeColor = exports.getStatusColor = exports.getPriorityColor = exports.isValidPassword = exports.isValidEmail = exports.getLocalizedText = exports.formatRelativeTime = exports.formatDate = void 0;
const constants_1 = require("./constants");
const formatDate = (date, locale = 'en') => {
    const d = new Date(date);
    const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    return d.toLocaleDateString(locale, options);
};
exports.formatDate = formatDate;
const formatRelativeTime = (date, locale = 'en') => {
    const d = new Date(date);
    const now = new Date();
    const diffMs = now.getTime() - d.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    if (diffMins < 1)
        return locale === 'ar' ? 'الآن' : locale === 'fr' ? 'maintenant' : 'now';
    if (diffMins < 60)
        return locale === 'ar' ? `منذ ${diffMins} دقيقة` : locale === 'fr' ? `il y a ${diffMins} min` : `${diffMins}m ago`;
    if (diffHours < 24)
        return locale === 'ar' ? `منذ ${diffHours} ساعة` : locale === 'fr' ? `il y a ${diffHours}h` : `${diffHours}h ago`;
    return locale === 'ar' ? `منذ ${diffDays} يوم` : locale === 'fr' ? `il y a ${diffDays}j` : `${diffDays}d ago`;
};
exports.formatRelativeTime = formatRelativeTime;
const getLocalizedText = (text, locale = 'en') => {
    return text[locale] || text.en;
};
exports.getLocalizedText = getLocalizedText;
const isValidEmail = (email) => {
    return constants_1.VALIDATION_RULES.EMAIL_REGEX.test(email);
};
exports.isValidEmail = isValidEmail;
const isValidPassword = (password) => {
    return password.length >= constants_1.VALIDATION_RULES.PASSWORD_MIN_LENGTH;
};
exports.isValidPassword = isValidPassword;
const getPriorityColor = (priority) => {
    return constants_1.COLORS.PRIORITY[priority];
};
exports.getPriorityColor = getPriorityColor;
const getStatusColor = (status) => {
    return constants_1.COLORS.STATUS[status];
};
exports.getStatusColor = getStatusColor;
const getIncidentTypeColor = (type) => {
    return constants_1.COLORS.INCIDENT_TYPE[type];
};
exports.getIncidentTypeColor = getIncidentTypeColor;
const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371;
    const dLat = toRadians(lat2 - lat1);
    const dLon = toRadians(lon2 - lon1);
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
};
exports.calculateDistance = calculateDistance;
const toRadians = (degrees) => {
    return degrees * (Math.PI / 180);
};
const formatFileSize = (bytes) => {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
exports.formatFileSize = formatFileSize;
const debounce = (func, wait) => {
    let timeout;
    return (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
};
exports.debounce = debounce;
const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
};
exports.generateId = generateId;
const sanitizeFilename = (filename) => {
    return filename.replace(/[^a-z0-9.-]/gi, '_').toLowerCase();
};
exports.sanitizeFilename = sanitizeFilename;
const isAllowedFileType = (mimeType, allowedTypes) => {
    return allowedTypes.includes(mimeType);
};
exports.isAllowedFileType = isAllowedFileType;
const truncateText = (text, maxLength) => {
    if (text.length <= maxLength)
        return text;
    return text.substr(0, maxLength) + '...';
};
exports.truncateText = truncateText;
const deepClone = (obj) => {
    return JSON.parse(JSON.stringify(obj));
};
exports.deepClone = deepClone;
const isMobile = (userAgent) => {
    if (!userAgent)
        return false;
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
};
exports.isMobile = isMobile;
const getLanguageFromHeader = (acceptLanguage) => {
    if (!acceptLanguage)
        return 'en';
    const lang = acceptLanguage.split(',')[0].split('-')[0];
    return lang || 'en';
};
exports.getLanguageFromHeader = getLanguageFromHeader;
//# sourceMappingURL=utils.js.map