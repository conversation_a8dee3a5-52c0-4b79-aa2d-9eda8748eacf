"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const middleware_1 = require("../middleware");
const express_validator_1 = require("express-validator");
const router = (0, express_1.Router)();
router.use(middleware_1.authenticate);
router.get('/stats', middleware_1.validateDateRange, controllers_1.getIncidentStats);
router.get('/', [middleware_1.validatePagination, middleware_1.validateDateRange], controllers_1.getIncidents);
router.post('/', middleware_1.validateIncidentCreation, controllers_1.createIncident);
router.get('/:id', (0, middleware_1.validateObjectId)('id'), controllers_1.getIncidentById);
router.put('/:id', [
    (0, middleware_1.validateObjectId)('id'),
    middleware_1.validateIncidentUpdate
], controllers_1.updateIncident);
router.delete('/:id', [
    middleware_1.requireAdminOrSupervisor,
    (0, middleware_1.validateObjectId)('id')
], controllers_1.deleteIncident);
router.put('/:id/assign', [
    middleware_1.requireAdminOrSupervisor,
    (0, middleware_1.validateObjectId)('id'),
    (0, express_validator_1.body)('assignedTo')
        .isMongoId()
        .withMessage('Invalid assigned officer ID')
], controllers_1.assignIncident);
exports.default = router;
//# sourceMappingURL=incidents.js.map