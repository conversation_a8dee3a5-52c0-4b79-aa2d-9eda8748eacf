"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertType = exports.Priority = exports.IncidentStatus = exports.IncidentType = exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["OFFICER"] = "officer";
    UserRole["SUPERVISOR"] = "supervisor";
    UserRole["ANALYST"] = "analyst";
    UserRole["ADMIN"] = "admin";
})(UserRole || (exports.UserRole = UserRole = {}));
var IncidentType;
(function (IncidentType) {
    IncidentType["CRIME"] = "crime";
    IncidentType["ACCIDENT"] = "accident";
    IncidentType["THREAT"] = "threat";
    IncidentType["SUSPICIOUS"] = "suspicious";
    IncidentType["EMERGENCY"] = "emergency";
    IncidentType["TRAFFIC"] = "traffic";
    IncidentType["DOMESTIC"] = "domestic";
    IncidentType["THEFT"] = "theft";
    IncidentType["ASSAULT"] = "assault";
    IncidentType["OTHER"] = "other";
})(IncidentType || (exports.IncidentType = IncidentType = {}));
var IncidentStatus;
(function (IncidentStatus) {
    IncidentStatus["PENDING"] = "pending";
    IncidentStatus["INVESTIGATING"] = "investigating";
    IncidentStatus["RESOLVED"] = "resolved";
    IncidentStatus["CLOSED"] = "closed";
})(IncidentStatus || (exports.IncidentStatus = IncidentStatus = {}));
var Priority;
(function (Priority) {
    Priority["LOW"] = "low";
    Priority["MEDIUM"] = "medium";
    Priority["HIGH"] = "high";
    Priority["CRITICAL"] = "critical";
})(Priority || (exports.Priority = Priority = {}));
var AlertType;
(function (AlertType) {
    AlertType["HIGH_INCIDENT_RATE"] = "high_incident_rate";
    AlertType["CRITICAL_INCIDENT"] = "critical_incident";
    AlertType["SYSTEM_ALERT"] = "system_alert";
})(AlertType || (exports.AlertType = AlertType = {}));
//# sourceMappingURL=types.js.map