{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../src/middleware/error.ts"], "names": [], "mappings": ";;;AACA,sCAAmC;AAUnC,MAAa,WAAY,SAAQ,KAAK;IAIpC,YAAY,OAAe,EAAE,aAAqB,GAAG;QACnD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAXD,kCAWC;AAKD,MAAM,eAAe,GAAG,CAAC,KAAU,EAAe,EAAE;IAClD,MAAM,OAAO,GAAG,WAAW,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC;IACxD,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACvC,CAAC,CAAC;AAKF,MAAM,uBAAuB,GAAG,CAAC,KAAU,EAAe,EAAE;IAC1D,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,MAAM,OAAO,GAAG,GAAG,KAAK,iBAAiB,CAAC;IAC1C,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACvC,CAAC,CAAC;AAKF,MAAM,qBAAqB,GAAG,CAAC,KAAU,EAAe,EAAE;IACxD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAC1E,MAAM,OAAO,GAAG,sBAAsB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC1D,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACvC,CAAC,CAAC;AAKF,MAAM,cAAc,GAAG,GAAgB,EAAE;IACvC,OAAO,IAAI,WAAW,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;AAC/C,CAAC,CAAC;AAEF,MAAM,qBAAqB,GAAG,GAAgB,EAAE;IAC9C,OAAO,IAAI,WAAW,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;AAC/C,CAAC,CAAC;AAKF,MAAM,YAAY,GAAG,CAAC,GAAa,EAAE,GAAa,EAAQ,EAAE;IAC1D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;QACrC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,GAAG,CAAC,OAAO;QAClB,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,OAAO,EAAE,GAAG;KACb,CAAC,CAAC;AACL,CAAC,CAAC;AAKF,MAAM,aAAa,GAAG,CAAC,GAAa,EAAE,GAAa,EAAQ,EAAE;IAE3D,IAAI,GAAG,CAAC,aAAa,EAAE,CAAC;QACtB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;YACrC,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,GAAG,CAAC,OAAO;SACnB,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QAEN,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAE7B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,sBAAsB;SAC9B,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAKK,MAAM,YAAY,GAAG,CAC1B,GAAQ,EACR,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;IACvB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;IAG5B,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAG7B,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QAC7B,KAAK,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;QACvB,KAAK,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACnC,KAAK,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACrC,KAAK,GAAG,cAAc,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACrC,KAAK,GAAG,qBAAqB,EAAE,CAAC;IAClC,CAAC;IAGD,IAAI,eAAM,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;QACtC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC3B,CAAC;SAAM,CAAC;QACN,aAAa,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC5B,CAAC;AACH,CAAC,CAAC;AA1CW,QAAA,YAAY,gBA0CvB;AAKK,MAAM,QAAQ,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAChF,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,SAAS,GAAG,CAAC,WAAW,YAAY,EAAE,GAAG,CAAC,CAAC;IACzE,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,CAAC,CAAC;AAHW,QAAA,QAAQ,YAGnB;AAKK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB"}