import { I18nText, IncidentType, Priority, IncidentStatus } from './types';
export declare const formatDate: (date: Date | string, locale?: string) => string;
export declare const formatRelativeTime: (date: Date | string, locale?: string) => string;
export declare const getLocalizedText: (text: I18nText, locale?: string) => string;
export declare const isValidEmail: (email: string) => boolean;
export declare const isValidPassword: (password: string) => boolean;
export declare const getPriorityColor: (priority: Priority) => string;
export declare const getStatusColor: (status: IncidentStatus) => string;
export declare const getIncidentTypeColor: (type: IncidentType) => string;
export declare const calculateDistance: (lat1: number, lon1: number, lat2: number, lon2: number) => number;
export declare const formatFileSize: (bytes: number) => string;
export declare const debounce: <T extends (...args: any[]) => any>(func: T, wait: number) => ((...args: Parameters<T>) => void);
export declare const generateId: () => string;
export declare const sanitizeFilename: (filename: string) => string;
export declare const isAllowedFileType: (mimeType: string, allowedTypes: string[]) => boolean;
export declare const truncateText: (text: string, maxLength: number) => string;
export declare const deepClone: <T>(obj: T) => T;
export declare const isMobile: (userAgent?: string) => boolean;
export declare const getLanguageFromHeader: (acceptLanguage?: string) => string;
//# sourceMappingURL=utils.d.ts.map