import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/models.dart';
import '../utils/constants.dart';

class StorageService {
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
  );

  // Token management (secure storage)
  Future<void> saveToken(String token) async {
    await _secureStorage.write(key: AppConstants.tokenKey, value: token);
  }

  Future<String?> getToken() async {
    return await _secureStorage.read(key: AppConstants.tokenKey);
  }

  Future<void> deleteToken() async {
    await _secureStorage.delete(key: AppConstants.tokenKey);
  }

  // User data management (regular storage)
  Future<void> saveUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userKey, json.encode(user.toJson()));
  }

  Future<User?> getUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(AppConstants.userKey);
    if (userJson != null) {
      return User.fromJson(json.decode(userJson));
    }
    return null;
  }

  Future<void> deleteUser() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userKey);
  }

  // Language preference
  Future<void> saveLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.languageKey, languageCode);
  }

  Future<String?> getLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConstants.languageKey);
  }

  // Clear all data (logout)
  Future<void> clearAll() async {
    await deleteToken();
    await deleteUser();
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
    await _secureStorage.deleteAll();
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await getToken();
    final user = await getUser();
    return token != null && user != null;
  }

  // Offline incidents storage
  Future<void> saveOfflineIncident(Incident incident) async {
    final prefs = await SharedPreferences.getInstance();
    final offlineIncidents = await getOfflineIncidents();
    offlineIncidents.add(incident);

    final incidentsJson = offlineIncidents.map((i) => i.toJson()).toList();
    await prefs.setString('offline_incidents', json.encode(incidentsJson));
  }

  Future<List<Incident>> getOfflineIncidents() async {
    final prefs = await SharedPreferences.getInstance();
    final incidentsJson = prefs.getString('offline_incidents');

    if (incidentsJson != null) {
      final List<dynamic> incidentsList = json.decode(incidentsJson);
      return incidentsList.map((i) => Incident.fromJson(i)).toList();
    }

    return [];
  }

  Future<void> removeOfflineIncident(String incidentId) async {
    final prefs = await SharedPreferences.getInstance();
    final offlineIncidents = await getOfflineIncidents();
    offlineIncidents.removeWhere((i) => i.id == incidentId);

    final incidentsJson = offlineIncidents.map((i) => i.toJson()).toList();
    await prefs.setString('offline_incidents', json.encode(incidentsJson));
  }

  Future<void> clearOfflineIncidents() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('offline_incidents');
  }

  // App settings
  Future<void> saveSetting(String key, dynamic value) async {
    final prefs = await SharedPreferences.getInstance();

    if (value is String) {
      await prefs.setString(key, value);
    } else if (value is int) {
      await prefs.setInt(key, value);
    } else if (value is double) {
      await prefs.setDouble(key, value);
    } else if (value is bool) {
      await prefs.setBool(key, value);
    } else {
      await prefs.setString(key, json.encode(value));
    }
  }

  Future<T?> getSetting<T>(String key) async {
    final prefs = await SharedPreferences.getInstance();

    if (T == String) {
      return prefs.getString(key) as T?;
    } else if (T == int) {
      return prefs.getInt(key) as T?;
    } else if (T == double) {
      return prefs.getDouble(key) as T?;
    } else if (T == bool) {
      return prefs.getBool(key) as T?;
    } else {
      final value = prefs.getString(key);
      if (value != null) {
        return json.decode(value) as T;
      }
    }

    return null;
  }
}
