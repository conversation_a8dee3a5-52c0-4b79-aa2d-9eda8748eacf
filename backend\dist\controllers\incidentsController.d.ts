import { Request, Response } from 'express';
export declare const createIncident: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getIncidents: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getIncidentById: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const updateIncident: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const deleteIncident: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getIncidentStats: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const assignIncident: (req: Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=incidentsController.d.ts.map