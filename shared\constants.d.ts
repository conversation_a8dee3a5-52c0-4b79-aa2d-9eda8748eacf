export declare const API_ENDPOINTS: {
    AUTH: {
        LOGIN: string;
        LOGOUT: string;
        REFRESH: string;
        PROFILE: string;
    };
    INCIDENTS: {
        BASE: string;
        STATS: string;
        EXPORT: string;
    };
    USERS: {
        BASE: string;
        PROFILE: string;
    };
    MEDIA: {
        UPLOAD: string;
        BASE: string;
    };
    ALERTS: string;
};
export declare const APP_CONFIG: {
    MAX_FILE_SIZE: number;
    ALLOWED_FILE_TYPES: string[];
    SUPPORTED_LANGUAGES: string[];
    DEFAULT_LANGUAGE: string;
    OFFLINE_SYNC_INTERVAL: number;
    MAP_DEFAULT_ZOOM: number;
    PAGINATION_LIMIT: number;
};
export declare const INCIDENT_TYPE_LABELS: {
    crime: {
        en: string;
        fr: string;
        ar: string;
    };
    accident: {
        en: string;
        fr: string;
        ar: string;
    };
    threat: {
        en: string;
        fr: string;
        ar: string;
    };
    suspicious: {
        en: string;
        fr: string;
        ar: string;
    };
    emergency: {
        en: string;
        fr: string;
        ar: string;
    };
    traffic: {
        en: string;
        fr: string;
        ar: string;
    };
    domestic: {
        en: string;
        fr: string;
        ar: string;
    };
    theft: {
        en: string;
        fr: string;
        ar: string;
    };
    assault: {
        en: string;
        fr: string;
        ar: string;
    };
    other: {
        en: string;
        fr: string;
        ar: string;
    };
};
export declare const PRIORITY_LABELS: {
    low: {
        en: string;
        fr: string;
        ar: string;
    };
    medium: {
        en: string;
        fr: string;
        ar: string;
    };
    high: {
        en: string;
        fr: string;
        ar: string;
    };
    critical: {
        en: string;
        fr: string;
        ar: string;
    };
};
export declare const STATUS_LABELS: {
    pending: {
        en: string;
        fr: string;
        ar: string;
    };
    investigating: {
        en: string;
        fr: string;
        ar: string;
    };
    resolved: {
        en: string;
        fr: string;
        ar: string;
    };
    closed: {
        en: string;
        fr: string;
        ar: string;
    };
};
export declare const ROLE_LABELS: {
    officer: {
        en: string;
        fr: string;
        ar: string;
    };
    supervisor: {
        en: string;
        fr: string;
        ar: string;
    };
    analyst: {
        en: string;
        fr: string;
        ar: string;
    };
    admin: {
        en: string;
        fr: string;
        ar: string;
    };
};
export declare const VALIDATION_RULES: {
    PASSWORD_MIN_LENGTH: number;
    TITLE_MAX_LENGTH: number;
    DESCRIPTION_MAX_LENGTH: number;
    TAG_MAX_LENGTH: number;
    MAX_TAGS: number;
    EMAIL_REGEX: RegExp;
    PHONE_REGEX: RegExp;
};
export declare const ERROR_MESSAGES: {
    UNAUTHORIZED: {
        en: string;
        fr: string;
        ar: string;
    };
    FORBIDDEN: {
        en: string;
        fr: string;
        ar: string;
    };
    NOT_FOUND: {
        en: string;
        fr: string;
        ar: string;
    };
    VALIDATION_ERROR: {
        en: string;
        fr: string;
        ar: string;
    };
    SERVER_ERROR: {
        en: string;
        fr: string;
        ar: string;
    };
    NETWORK_ERROR: {
        en: string;
        fr: string;
        ar: string;
    };
    FILE_TOO_LARGE: {
        en: string;
        fr: string;
        ar: string;
    };
    INVALID_FILE_TYPE: {
        en: string;
        fr: string;
        ar: string;
    };
};
export declare const SUCCESS_MESSAGES: {
    INCIDENT_CREATED: {
        en: string;
        fr: string;
        ar: string;
    };
    INCIDENT_UPDATED: {
        en: string;
        fr: string;
        ar: string;
    };
    LOGIN_SUCCESS: {
        en: string;
        fr: string;
        ar: string;
    };
    LOGOUT_SUCCESS: {
        en: string;
        fr: string;
        ar: string;
    };
    DATA_SYNCED: {
        en: string;
        fr: string;
        ar: string;
    };
};
export declare const COLORS: {
    PRIORITY: {
        low: string;
        medium: string;
        high: string;
        critical: string;
    };
    STATUS: {
        pending: string;
        investigating: string;
        resolved: string;
        closed: string;
    };
    INCIDENT_TYPE: {
        crime: string;
        accident: string;
        threat: string;
        suspicious: string;
        emergency: string;
        traffic: string;
        domestic: string;
        theft: string;
        assault: string;
        other: string;
    };
};
//# sourceMappingURL=constants.d.ts.map