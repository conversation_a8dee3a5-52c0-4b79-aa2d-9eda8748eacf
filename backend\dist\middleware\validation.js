"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateDateRange = exports.validatePagination = exports.validateObjectId = exports.validateIncidentUpdate = exports.validateIncidentCreation = exports.validateUserRegistration = exports.validateLogin = exports.handleValidationErrors = void 0;
const express_validator_1 = require("express-validator");
const types_1 = require("../shared/types");
const handleValidationErrors = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        res.status(400).json({
            success: false,
            error: 'Validation failed',
            details: errors.array()
        });
        return;
    }
    next();
};
exports.handleValidationErrors = handleValidationErrors;
exports.validateLogin = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long'),
    exports.handleValidationErrors
];
exports.validateUserRegistration = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 8 })
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    (0, express_validator_1.body)('firstName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('lastName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('role')
        .isIn(Object.values(types_1.UserRole))
        .withMessage('Invalid role'),
    (0, express_validator_1.body)('unit')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Unit name cannot exceed 100 characters'),
    (0, express_validator_1.body)('badgeNumber')
        .optional()
        .trim()
        .isLength({ max: 20 })
        .withMessage('Badge number cannot exceed 20 characters'),
    exports.handleValidationErrors
];
exports.validateIncidentCreation = [
    (0, express_validator_1.body)('type')
        .isIn(Object.values(types_1.IncidentType))
        .withMessage('Invalid incident type'),
    (0, express_validator_1.body)('title')
        .trim()
        .isLength({ min: 5, max: 100 })
        .withMessage('Title must be between 5 and 100 characters'),
    (0, express_validator_1.body)('description')
        .trim()
        .isLength({ min: 10, max: 2000 })
        .withMessage('Description must be between 10 and 2000 characters'),
    (0, express_validator_1.body)('location.latitude')
        .isFloat({ min: -90, max: 90 })
        .withMessage('Latitude must be between -90 and 90'),
    (0, express_validator_1.body)('location.longitude')
        .isFloat({ min: -180, max: 180 })
        .withMessage('Longitude must be between -180 and 180'),
    (0, express_validator_1.body)('location.address')
        .optional()
        .trim()
        .isLength({ max: 200 })
        .withMessage('Address cannot exceed 200 characters'),
    (0, express_validator_1.body)('location.region')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Region cannot exceed 100 characters'),
    (0, express_validator_1.body)('location.city')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('City cannot exceed 100 characters'),
    (0, express_validator_1.body)('priority')
        .isIn(Object.values(types_1.Priority))
        .withMessage('Invalid priority level'),
    (0, express_validator_1.body)('tags')
        .optional()
        .isArray({ max: 10 })
        .withMessage('Maximum 10 tags allowed'),
    (0, express_validator_1.body)('tags.*')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('Each tag cannot exceed 50 characters'),
    exports.handleValidationErrors
];
exports.validateIncidentUpdate = [
    (0, express_validator_1.body)('title')
        .optional()
        .trim()
        .isLength({ min: 5, max: 100 })
        .withMessage('Title must be between 5 and 100 characters'),
    (0, express_validator_1.body)('description')
        .optional()
        .trim()
        .isLength({ min: 10, max: 2000 })
        .withMessage('Description must be between 10 and 2000 characters'),
    (0, express_validator_1.body)('status')
        .optional()
        .isIn(Object.values(types_1.IncidentStatus))
        .withMessage('Invalid status'),
    (0, express_validator_1.body)('priority')
        .optional()
        .isIn(Object.values(types_1.Priority))
        .withMessage('Invalid priority level'),
    (0, express_validator_1.body)('tags')
        .optional()
        .isArray({ max: 10 })
        .withMessage('Maximum 10 tags allowed'),
    (0, express_validator_1.body)('tags.*')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('Each tag cannot exceed 50 characters'),
    exports.handleValidationErrors
];
const validateObjectId = (paramName) => [
    (0, express_validator_1.param)(paramName)
        .isMongoId()
        .withMessage(`Invalid ${paramName} format`),
    exports.handleValidationErrors
];
exports.validateObjectId = validateObjectId;
exports.validatePagination = [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    exports.handleValidationErrors
];
exports.validateDateRange = [
    (0, express_validator_1.query)('dateFrom')
        .optional()
        .isISO8601()
        .withMessage('Invalid dateFrom format'),
    (0, express_validator_1.query)('dateTo')
        .optional()
        .isISO8601()
        .withMessage('Invalid dateTo format'),
    exports.handleValidationErrors
];
//# sourceMappingURL=validation.js.map