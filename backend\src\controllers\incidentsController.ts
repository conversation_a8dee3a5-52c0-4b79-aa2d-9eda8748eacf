import { Request, Response } from 'express';
import { Incident } from '../models';
import { async<PERSON><PERSON><PERSON>, CustomError } from '../middleware';
import {
  CreateIncidentRequest,
  UpdateIncidentRequest,
  IncidentFilter,
  PaginatedResponse,
  IncidentStats,
  IncidentType,
  IncidentStatus,
  Priority
} from '../shared/types';

/**
 * @desc    Create new incident
 * @route   POST /api/incidents
 * @access  Private
 */
export const createIncident = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const user = req.user!;
  const incidentData: CreateIncidentRequest = req.body;

  const incident = await Incident.create({
    ...incidentData,
    reportedBy: user._id,
    reportedAt: new Date()
  });

  await incident.populate('reportedBy', 'firstName lastName email badgeNumber');

  res.status(201).json({
    success: true,
    data: incident,
    message: 'Incident created successfully'
  });
});

/**
 * @desc    Get all incidents with filtering and pagination
 * @route   GET /api/incidents
 * @access  Private
 */
export const getIncidents = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const {
    type,
    status,
    priority,
    region,
    dateFrom,
    dateTo,
    reportedBy,
    search,
    page = 1,
    limit = 20
  } = req.query as any;

  // Build filter object
  const filter: any = {};

  if (type) {
    filter.type = Array.isArray(type) ? { $in: type } : type;
  }

  if (status) {
    filter.status = Array.isArray(status) ? { $in: status } : status;
  }

  if (priority) {
    filter.priority = Array.isArray(priority) ? { $in: priority } : priority;
  }

  if (region) {
    filter['location.region'] = Array.isArray(region) ? { $in: region } : region;
  }

  if (reportedBy) {
    filter.reportedBy = Array.isArray(reportedBy) ? { $in: reportedBy } : reportedBy;
  }

  // Date range filter
  if (dateFrom || dateTo) {
    filter.reportedAt = {};
    if (dateFrom) filter.reportedAt.$gte = new Date(dateFrom);
    if (dateTo) filter.reportedAt.$lte = new Date(dateTo);
  }

  // Text search
  if (search) {
    filter.$text = { $search: search };
  }

  // Pagination
  const pageNum = parseInt(page as string, 10);
  const limitNum = parseInt(limit as string, 10);
  const skip = (pageNum - 1) * limitNum;

  // Execute query
  const [incidents, total] = await Promise.all([
    Incident.find(filter)
      .populate('reportedBy', 'firstName lastName email badgeNumber')
      .populate('assignedTo', 'firstName lastName email')
      .sort({ reportedAt: -1 })
      .skip(skip)
      .limit(limitNum),
    Incident.countDocuments(filter)
  ]);

  const response: PaginatedResponse<typeof incidents[0]> = {
    data: incidents,
    total,
    page: pageNum,
    limit: limitNum,
    totalPages: Math.ceil(total / limitNum)
  };

  res.status(200).json({
    success: true,
    data: response
  });
});

/**
 * @desc    Get incident by ID
 * @route   GET /api/incidents/:id
 * @access  Private
 */
export const getIncidentById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  const incident = await Incident.findById(id)
    .populate('reportedBy', 'firstName lastName email badgeNumber unit')
    .populate('assignedTo', 'firstName lastName email');

  if (!incident) {
    throw new CustomError('Incident not found', 404);
  }

  res.status(200).json({
    success: true,
    data: incident
  });
});

/**
 * @desc    Update incident
 * @route   PUT /api/incidents/:id
 * @access  Private
 */
export const updateIncident = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData: UpdateIncidentRequest = req.body;
  const user = req.user!;

  const incident = await Incident.findById(id);

  if (!incident) {
    throw new CustomError('Incident not found', 404);
  }

  // Check permissions - only reporter, assigned officer, or supervisor/admin can update
  const canUpdate =
    incident.reportedBy.toString() === (user._id as string).toString() ||
    incident.assignedTo?.toString() === (user._id as string).toString() ||
    ['supervisor', 'admin'].includes(user.role);

  if (!canUpdate) {
    throw new CustomError('Not authorized to update this incident', 403);
  }

  // Update fields
  Object.assign(incident, updateData);

  // Set resolved date if status changed to resolved
  if (updateData.status === IncidentStatus.RESOLVED && incident.status !== IncidentStatus.RESOLVED) {
    incident.resolvedAt = new Date();
  }

  await incident.save();
  await incident.populate('reportedBy', 'firstName lastName email badgeNumber');
  await incident.populate('assignedTo', 'firstName lastName email');

  res.status(200).json({
    success: true,
    data: incident,
    message: 'Incident updated successfully'
  });
});

/**
 * @desc    Delete incident
 * @route   DELETE /api/incidents/:id
 * @access  Private (Admin/Supervisor only)
 */
export const deleteIncident = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  const incident = await Incident.findById(id);

  if (!incident) {
    throw new CustomError('Incident not found', 404);
  }

  await Incident.findByIdAndDelete(id);

  res.status(200).json({
    success: true,
    message: 'Incident deleted successfully'
  });
});

/**
 * @desc    Get incident statistics
 * @route   GET /api/incidents/stats
 * @access  Private
 */
export const getIncidentStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { dateFrom, dateTo, region } = req.query as any;

  // Build match filter
  const matchFilter: any = {};

  if (dateFrom || dateTo) {
    matchFilter.reportedAt = {};
    if (dateFrom) matchFilter.reportedAt.$gte = new Date(dateFrom);
    if (dateTo) matchFilter.reportedAt.$lte = new Date(dateTo);
  }

  if (region) {
    matchFilter['location.region'] = region;
  }

  // Aggregation pipeline
  const [
    totalIncidents,
    byType,
    byStatus,
    byPriority,
    byRegion,
    recentTrends
  ] = await Promise.all([
    // Total incidents
    Incident.countDocuments(matchFilter),

    // By type
    Incident.aggregate([
      { $match: matchFilter },
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]),

    // By status
    Incident.aggregate([
      { $match: matchFilter },
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]),

    // By priority
    Incident.aggregate([
      { $match: matchFilter },
      { $group: { _id: '$priority', count: { $sum: 1 } } }
    ]),

    // By region
    Incident.aggregate([
      { $match: matchFilter },
      { $group: { _id: '$location.region', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]),

    // Recent trends (last 30 days)
    Incident.aggregate([
      {
        $match: {
          ...matchFilter,
          reportedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$reportedAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ])
  ]);

  // Format results
  const stats: IncidentStats = {
    totalIncidents,
    byType: Object.values(IncidentType).reduce((acc, type) => {
      acc[type] = byType.find(item => item._id === type)?.count || 0;
      return acc;
    }, {} as Record<IncidentType, number>),
    byStatus: Object.values(IncidentStatus).reduce((acc, status) => {
      acc[status] = byStatus.find(item => item._id === status)?.count || 0;
      return acc;
    }, {} as Record<IncidentStatus, number>),
    byPriority: Object.values(Priority).reduce((acc, priority) => {
      acc[priority] = byPriority.find(item => item._id === priority)?.count || 0;
      return acc;
    }, {} as Record<Priority, number>),
    byRegion: byRegion.reduce((acc, item) => {
      if (item._id) acc[item._id] = item.count;
      return acc;
    }, {} as Record<string, number>),
    recentTrends: recentTrends.map(item => ({
      date: item._id,
      count: item.count
    }))
  };

  res.status(200).json({
    success: true,
    data: stats
  });
});

/**
 * @desc    Assign incident to officer
 * @route   PUT /api/incidents/:id/assign
 * @access  Private (Supervisor/Admin only)
 */
export const assignIncident = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;
  const { assignedTo } = req.body;

  const incident = await Incident.findById(id);

  if (!incident) {
    throw new CustomError('Incident not found', 404);
  }

  incident.assignedTo = assignedTo;
  incident.status = IncidentStatus.INVESTIGATING;
  await incident.save();

  await incident.populate('assignedTo', 'firstName lastName email');

  res.status(200).json({
    success: true,
    data: incident,
    message: 'Incident assigned successfully'
  });
});
