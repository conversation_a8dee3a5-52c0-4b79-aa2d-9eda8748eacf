"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Incident = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const types_1 = require("../shared/types");
const locationSchema = new mongoose_1.Schema({
    latitude: {
        type: Number,
        required: [true, 'Latitude is required'],
        min: [-90, 'Latitude must be between -90 and 90'],
        max: [90, 'Latitude must be between -90 and 90']
    },
    longitude: {
        type: Number,
        required: [true, 'Longitude is required'],
        min: [-180, 'Longitude must be between -180 and 180'],
        max: [180, 'Longitude must be between -180 and 180']
    },
    address: {
        type: String,
        trim: true,
        maxlength: [200, 'Address cannot exceed 200 characters']
    },
    region: {
        type: String,
        trim: true,
        maxlength: [100, 'Region cannot exceed 100 characters']
    },
    city: {
        type: String,
        trim: true,
        maxlength: [100, 'City cannot exceed 100 characters']
    },
    country: {
        type: String,
        trim: true,
        maxlength: [100, 'Country cannot exceed 100 characters']
    }
}, { _id: false });
const mediaFileSchema = new mongoose_1.Schema({
    id: {
        type: String,
        required: true
    },
    filename: {
        type: String,
        required: [true, 'Filename is required']
    },
    originalName: {
        type: String,
        required: [true, 'Original name is required']
    },
    mimeType: {
        type: String,
        required: [true, 'MIME type is required']
    },
    size: {
        type: Number,
        required: [true, 'File size is required'],
        min: [0, 'File size must be positive']
    },
    url: {
        type: String,
        required: [true, 'File URL is required']
    },
    uploadedAt: {
        type: Date,
        default: Date.now
    }
}, { _id: false });
const incidentSchema = new mongoose_1.Schema({
    type: {
        type: String,
        enum: Object.values(types_1.IncidentType),
        required: [true, 'Incident type is required']
    },
    title: {
        type: String,
        required: [true, 'Title is required'],
        trim: true,
        maxlength: [100, 'Title cannot exceed 100 characters']
    },
    description: {
        type: String,
        required: [true, 'Description is required'],
        trim: true,
        maxlength: [2000, 'Description cannot exceed 2000 characters']
    },
    location: {
        type: locationSchema,
        required: [true, 'Location is required']
    },
    reportedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Reporter is required']
    },
    reportedAt: {
        type: Date,
        default: Date.now
    },
    status: {
        type: String,
        enum: Object.values(types_1.IncidentStatus),
        default: types_1.IncidentStatus.PENDING
    },
    priority: {
        type: String,
        enum: Object.values(types_1.Priority),
        required: [true, 'Priority is required'],
        default: types_1.Priority.MEDIUM
    },
    media: [mediaFileSchema],
    tags: [{
            type: String,
            trim: true,
            maxlength: [50, 'Tag cannot exceed 50 characters']
        }],
    isOfflineCreated: {
        type: Boolean,
        default: false
    },
    syncedAt: {
        type: Date
    },
    assignedTo: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User'
    },
    resolvedAt: {
        type: Date
    },
    notes: [{
            type: String,
            trim: true,
            maxlength: [500, 'Note cannot exceed 500 characters']
        }]
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.__v;
            return ret;
        }
    }
});
incidentSchema.index({ type: 1 });
incidentSchema.index({ status: 1 });
incidentSchema.index({ priority: 1 });
incidentSchema.index({ reportedBy: 1 });
incidentSchema.index({ reportedAt: -1 });
incidentSchema.index({ 'location.region': 1 });
incidentSchema.index({ 'location.city': 1 });
incidentSchema.index({ tags: 1 });
incidentSchema.index({ isOfflineCreated: 1 });
incidentSchema.index({ status: 1, priority: 1 });
incidentSchema.index({ type: 1, reportedAt: -1 });
incidentSchema.index({ 'location.region': 1, reportedAt: -1 });
incidentSchema.index({
    title: 'text',
    description: 'text',
    tags: 'text',
    'location.address': 'text'
});
incidentSchema.index({ 'location.latitude': 1, 'location.longitude': 1 });
exports.Incident = mongoose_1.default.model('Incident', incidentSchema);
//# sourceMappingURL=Incident.js.map