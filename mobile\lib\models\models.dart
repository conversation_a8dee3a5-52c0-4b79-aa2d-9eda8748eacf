// Shared models for the Police Reporting System

enum UserRole { officer, supervisor, analyst, admin }

class User {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final UserRole role;
  final String? unit;
  final String? badgeNumber;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.role,
    this.unit,
    this.badgeNumber,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? json['_id'],
      email: json['email'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      role: UserRole.values.firstWhere(
        (e) => e.toString().split('.').last == json['role'],
        orElse: () => UserRole.officer,
      ),
      unit: json['unit'],
      badgeNumber: json['badgeNumber'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'role': role.toString().split('.').last,
      'unit': unit,
      'badgeNumber': badgeNumber,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  String get fullName => '$firstName $lastName';
}

enum IncidentType {
  crime,
  accident,
  threat,
  suspicious,
  emergency,
  traffic,
  domestic,
  theft,
  assault,
  other,
}

enum IncidentStatus { pending, investigating, resolved, closed }

enum Priority { low, medium, high, critical }

class Location {
  final double latitude;
  final double longitude;
  final String? address;
  final String? region;
  final String? city;
  final String? country;

  Location({
    required this.latitude,
    required this.longitude,
    this.address,
    this.region,
    this.city,
    this.country,
  });

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      address: json['address'],
      region: json['region'],
      city: json['city'],
      country: json['country'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'region': region,
      'city': city,
      'country': country,
    };
  }
}

class MediaFile {
  final String id;
  final String filename;
  final String originalName;
  final String mimeType;
  final int size;
  final String url;
  final DateTime uploadedAt;

  MediaFile({
    required this.id,
    required this.filename,
    required this.originalName,
    required this.mimeType,
    required this.size,
    required this.url,
    required this.uploadedAt,
  });

  factory MediaFile.fromJson(Map<String, dynamic> json) {
    return MediaFile(
      id: json['id'],
      filename: json['filename'],
      originalName: json['originalName'],
      mimeType: json['mimeType'],
      size: json['size'],
      url: json['url'],
      uploadedAt: DateTime.parse(json['uploadedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'filename': filename,
      'originalName': originalName,
      'mimeType': mimeType,
      'size': size,
      'url': url,
      'uploadedAt': uploadedAt.toIso8601String(),
    };
  }

  bool get isImage => mimeType.startsWith('image/');
  bool get isVideo => mimeType.startsWith('video/');
}

class Incident {
  final String id;
  final IncidentType type;
  final String title;
  final String description;
  final Location location;
  final String reportedBy;
  final DateTime reportedAt;
  final IncidentStatus status;
  final Priority priority;
  final List<MediaFile> media;
  final List<String> tags;
  final bool isOfflineCreated;
  final DateTime? syncedAt;
  final String? assignedTo;
  final DateTime? resolvedAt;
  final List<String> notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Incident({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.location,
    required this.reportedBy,
    required this.reportedAt,
    required this.status,
    required this.priority,
    required this.media,
    required this.tags,
    required this.isOfflineCreated,
    this.syncedAt,
    this.assignedTo,
    this.resolvedAt,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Incident.fromJson(Map<String, dynamic> json) {
    return Incident(
      id: json['id'] ?? json['_id'],
      type: IncidentType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => IncidentType.other,
      ),
      title: json['title'],
      description: json['description'],
      location: Location.fromJson(json['location']),
      reportedBy: json['reportedBy'],
      reportedAt: DateTime.parse(json['reportedAt']),
      status: IncidentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => IncidentStatus.pending,
      ),
      priority: Priority.values.firstWhere(
        (e) => e.toString().split('.').last == json['priority'],
        orElse: () => Priority.medium,
      ),
      media:
          (json['media'] as List<dynamic>?)
              ?.map((m) => MediaFile.fromJson(m))
              .toList() ??
          [],
      tags: List<String>.from(json['tags'] ?? []),
      isOfflineCreated: json['isOfflineCreated'] ?? false,
      syncedAt: json['syncedAt'] != null
          ? DateTime.parse(json['syncedAt'])
          : null,
      assignedTo: json['assignedTo'],
      resolvedAt: json['resolvedAt'] != null
          ? DateTime.parse(json['resolvedAt'])
          : null,
      notes: List<String>.from(json['notes'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'title': title,
      'description': description,
      'location': location.toJson(),
      'reportedBy': reportedBy,
      'reportedAt': reportedAt.toIso8601String(),
      'status': status.toString().split('.').last,
      'priority': priority.toString().split('.').last,
      'media': media.map((m) => m.toJson()).toList(),
      'tags': tags,
      'isOfflineCreated': isOfflineCreated,
      'syncedAt': syncedAt?.toIso8601String(),
      'assignedTo': assignedTo,
      'resolvedAt': resolvedAt?.toIso8601String(),
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  bool get hasMedia => media.isNotEmpty;
  bool get hasImages => media.any((m) => m.isImage);
  bool get hasVideos => media.any((m) => m.isVideo);
  bool get isResolved =>
      status == IncidentStatus.resolved || status == IncidentStatus.closed;
  bool get needsSync => isOfflineCreated && syncedAt == null;
}

class AuthResponse {
  final String token;
  final User user;
  final int expiresIn;

  AuthResponse({
    required this.token,
    required this.user,
    required this.expiresIn,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      token: json['token'],
      user: User.fromJson(json['user']),
      expiresIn: json['expiresIn'],
    );
  }
}

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final String? error;

  ApiResponse({required this.success, this.data, this.message, this.error});

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : json['data'],
      message: json['message'],
      error: json['error'],
    );
  }
}
