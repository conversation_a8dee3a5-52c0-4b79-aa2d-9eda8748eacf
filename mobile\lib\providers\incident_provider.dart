import 'package:flutter/foundation.dart';
import 'dart:io';
import '../models/models.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

class IncidentProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  final StorageService _storageService = StorageService();

  List<Incident> _incidents = [];
  List<Incident> _offlineIncidents = [];
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic> _filters = {};

  // Getters
  List<Incident> get incidents => _incidents;
  List<Incident> get offlineIncidents => _offlineIncidents;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic> get filters => _filters;

  // Initialize
  Future<void> initialize() async {
    await loadOfflineIncidents();
    await loadIncidents();
  }

  // Load incidents from API
  Future<void> loadIncidents({Map<String, dynamic>? filters}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.getIncidents(filters: filters);

      if (response.success && response.data != null) {
        _incidents = response.data!;
        _filters = filters ?? {};
        notifyListeners();
      } else {
        _setError(response.error ?? 'Failed to load incidents');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load offline incidents
  Future<void> loadOfflineIncidents() async {
    try {
      _offlineIncidents = await _storageService.getOfflineIncidents();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading offline incidents: $e');
    }
  }

  // Create new incident
  Future<bool> createIncident({
    required IncidentType type,
    required String title,
    required String description,
    required double latitude,
    required double longitude,
    String? address,
    String? region,
    String? city,
    required Priority priority,
    List<String>? tags,
    List<File>? mediaFiles,
    bool isOffline = false,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Prepare incident data
      final incidentData = {
        'type': type.toString().split('.').last,
        'title': title,
        'description': description,
        'location': {
          'latitude': latitude,
          'longitude': longitude,
          'address': address,
          'region': region,
          'city': city,
        },
        'priority': priority.toString().split('.').last,
        'tags': tags ?? [],
      };

      if (isOffline) {
        // Save offline incident
        final offlineIncident = Incident(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: type,
          title: title,
          description: description,
          location: Location(
            latitude: latitude,
            longitude: longitude,
            address: address,
            region: region,
            city: city,
          ),
          reportedBy: 'offline_user',
          reportedAt: DateTime.now(),
          status: IncidentStatus.pending,
          priority: priority,
          media: [],
          tags: tags ?? [],
          isOfflineCreated: true,
          notes: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _storageService.saveOfflineIncident(offlineIncident);
        await loadOfflineIncidents();
        return true;
      } else {
        // Upload media files first if any
        if (mediaFiles != null && mediaFiles.isNotEmpty) {
          final mediaUrls = <String>[];
          for (final file in mediaFiles) {
            final uploadResponse = await _apiService.uploadFile(file);
            if (uploadResponse.success && uploadResponse.data != null) {
              mediaUrls.add(uploadResponse.data!);
            }
          }
          incidentData['mediaUrls'] = mediaUrls;
        }

        final response = await _apiService.createIncident(incidentData);

        if (response.success && response.data != null) {
          // Add to local list
          _incidents.insert(0, response.data!);
          notifyListeners();
          return true;
        } else {
          _setError(response.error ?? 'Failed to create incident');
          return false;
        }
      }
    } catch (e) {
      _setError('Error creating incident: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update incident
  Future<bool> updateIncident(String id, Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.updateIncident(id, data);

      if (response.success && response.data != null) {
        // Update local list
        final index = _incidents.indexWhere((i) => i.id == id);
        if (index != -1) {
          _incidents[index] = response.data!;
          notifyListeners();
        }
        return true;
      } else {
        _setError(response.error ?? 'Failed to update incident');
        return false;
      }
    } catch (e) {
      _setError('Error updating incident: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Sync offline incidents
  Future<void> syncOfflineIncidents() async {
    if (_offlineIncidents.isEmpty) return;

    for (final incident in List.from(_offlineIncidents)) {
      try {
        final incidentData = {
          'type': incident.type.toString().split('.').last,
          'title': incident.title,
          'description': incident.description,
          'location': incident.location.toJson(),
          'priority': incident.priority.toString().split('.').last,
          'tags': incident.tags,
        };

        final response = await _apiService.createIncident(incidentData);

        if (response.success) {
          // Remove from offline storage
          await _storageService.removeOfflineIncident(incident.id);
          _offlineIncidents.removeWhere((i) => i.id == incident.id);
        }
      } catch (e) {
        debugPrint('Error syncing incident ${incident.id}: $e');
      }
    }

    notifyListeners();
  }

  // Get incident by ID
  Future<Incident?> getIncident(String id) async {
    try {
      final response = await _apiService.getIncident(id);
      return response.success ? response.data : null;
    } catch (e) {
      debugPrint('Error getting incident: $e');
      return null;
    }
  }

  // Apply filters
  void applyFilters(Map<String, dynamic> filters) {
    _filters = filters;
    loadIncidents(filters: filters);
  }

  // Clear filters
  void clearFilters() {
    _filters.clear();
    loadIncidents();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Get incidents by status
  List<Incident> getIncidentsByStatus(IncidentStatus status) {
    return _incidents.where((i) => i.status == status).toList();
  }

  // Get incidents by priority
  List<Incident> getIncidentsByPriority(Priority priority) {
    return _incidents.where((i) => i.priority == priority).toList();
  }

  // Get incidents count
  int get totalIncidents => _incidents.length;
  int get pendingIncidents =>
      getIncidentsByStatus(IncidentStatus.pending).length;
  int get offlineIncidentsCount => _offlineIncidents.length;
}
