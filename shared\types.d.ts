export interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    unit?: string;
    badgeNumber?: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum UserRole {
    OFFICER = "officer",
    SUPERVISOR = "supervisor",
    ANALYST = "analyst",
    ADMIN = "admin"
}
export interface Incident {
    id: string;
    type: IncidentType;
    title: string;
    description: string;
    location: Location;
    reportedBy: string;
    reportedAt: Date;
    status: IncidentStatus;
    priority: Priority;
    media: MediaFile[];
    tags: string[];
    isOfflineCreated: boolean;
    syncedAt?: Date;
    updatedAt: Date;
}
export declare enum IncidentType {
    CRIME = "crime",
    ACCIDENT = "accident",
    THREAT = "threat",
    SUSPICIOUS = "suspicious",
    EMERGENCY = "emergency",
    TRAFFIC = "traffic",
    DOMESTIC = "domestic",
    THEFT = "theft",
    ASSAULT = "assault",
    OTHER = "other"
}
export declare enum IncidentStatus {
    PENDING = "pending",
    INVESTIGATING = "investigating",
    RESOLVED = "resolved",
    CLOSED = "closed"
}
export declare enum Priority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    CRITICAL = "critical"
}
export interface Location {
    latitude: number;
    longitude: number;
    address?: string;
    region?: string;
    city?: string;
    country?: string;
}
export interface MediaFile {
    id: string;
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    url: string;
    uploadedAt: Date;
}
export interface AuthResponse {
    token: string;
    user: User;
    expiresIn: number;
}
export interface LoginRequest {
    email: string;
    password: string;
}
export interface CreateIncidentRequest {
    type: IncidentType;
    title: string;
    description: string;
    location: Location;
    priority: Priority;
    tags?: string[];
    mediaFiles?: File[];
}
export interface UpdateIncidentRequest {
    title?: string;
    description?: string;
    status?: IncidentStatus;
    priority?: Priority;
    tags?: string[];
}
export interface IncidentFilter {
    type?: IncidentType[];
    status?: IncidentStatus[];
    priority?: Priority[];
    region?: string[];
    dateFrom?: Date;
    dateTo?: Date;
    reportedBy?: string[];
    search?: string;
    page?: number;
    limit?: number;
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface IncidentStats {
    totalIncidents: number;
    byType: Record<IncidentType, number>;
    byStatus: Record<IncidentStatus, number>;
    byPriority: Record<Priority, number>;
    byRegion: Record<string, number>;
    recentTrends: {
        date: string;
        count: number;
    }[];
}
export interface Alert {
    id: string;
    type: AlertType;
    message: string;
    region?: string;
    threshold: number;
    currentValue: number;
    isActive: boolean;
    createdAt: Date;
}
export declare enum AlertType {
    HIGH_INCIDENT_RATE = "high_incident_rate",
    CRITICAL_INCIDENT = "critical_incident",
    SYSTEM_ALERT = "system_alert"
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}
export interface I18nText {
    en: string;
    fr: string;
    ar: string;
}
export interface AppConfig {
    supportedLanguages: string[];
    defaultLanguage: string;
    mapProvider: 'google' | 'leaflet';
    maxFileSize: number;
    allowedFileTypes: string[];
    offlineSyncInterval: number;
}
//# sourceMappingURL=types.d.ts.map