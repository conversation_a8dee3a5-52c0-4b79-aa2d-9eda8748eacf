"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logout = exports.changePassword = exports.updateProfile = exports.getProfile = exports.register = exports.login = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const models_1 = require("../models");
const config_1 = require("../config");
const middleware_1 = require("../middleware");
const generateToken = (userId, email, role) => {
    return jsonwebtoken_1.default.sign({ userId, email, role }, config_1.config.JWT_SECRET, { expiresIn: config_1.config.JWT_EXPIRES_IN });
};
const generateRefreshToken = (userId) => {
    return jsonwebtoken_1.default.sign({ userId }, config_1.config.JWT_REFRESH_SECRET, { expiresIn: config_1.config.JWT_REFRESH_EXPIRES_IN });
};
exports.login = (0, middleware_1.asyncHandler)(async (req, res) => {
    const { email, password } = req.body;
    const user = await models_1.User.findOne({ email }).select('+password');
    if (!user || !user.isActive) {
        throw new middleware_1.CustomError('Invalid credentials', 401);
    }
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
        throw new middleware_1.CustomError('Invalid credentials', 401);
    }
    user.lastLogin = new Date();
    await user.save();
    const token = generateToken(user._id.toString(), user.email, user.role);
    const refreshToken = generateRefreshToken(user._id.toString());
    user.generateRefreshToken();
    await user.save();
    const response = {
        token,
        user: user.toJSON(),
        expiresIn: 7 * 24 * 60 * 60
    };
    res.status(200).json({
        success: true,
        data: response,
        message: 'Login successful'
    });
});
exports.register = (0, middleware_1.asyncHandler)(async (req, res) => {
    const { email, password, firstName, lastName, role, unit, badgeNumber } = req.body;
    const existingUser = await models_1.User.findOne({ email });
    if (existingUser) {
        throw new middleware_1.CustomError('User already exists with this email', 400);
    }
    if (badgeNumber) {
        const existingBadge = await models_1.User.findOne({ badgeNumber });
        if (existingBadge) {
            throw new middleware_1.CustomError('Badge number already exists', 400);
        }
    }
    const user = await models_1.User.create({
        email,
        password,
        firstName,
        lastName,
        role,
        unit,
        badgeNumber
    });
    res.status(201).json({
        success: true,
        data: user.toJSON(),
        message: 'User registered successfully'
    });
});
exports.getProfile = (0, middleware_1.asyncHandler)(async (req, res) => {
    const user = req.user;
    res.status(200).json({
        success: true,
        data: user.toJSON()
    });
});
exports.updateProfile = (0, middleware_1.asyncHandler)(async (req, res) => {
    const user = req.user;
    const { firstName, lastName, unit } = req.body;
    if (firstName)
        user.firstName = firstName;
    if (lastName)
        user.lastName = lastName;
    if (unit !== undefined)
        user.unit = unit;
    await user.save();
    res.status(200).json({
        success: true,
        data: user.toJSON(),
        message: 'Profile updated successfully'
    });
});
exports.changePassword = (0, middleware_1.asyncHandler)(async (req, res) => {
    const user = await models_1.User.findById(req.user._id).select('+password');
    const { currentPassword, newPassword } = req.body;
    if (!user) {
        throw new middleware_1.CustomError('User not found', 404);
    }
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
        throw new middleware_1.CustomError('Current password is incorrect', 400);
    }
    user.password = newPassword;
    await user.save();
    res.status(200).json({
        success: true,
        message: 'Password changed successfully'
    });
});
exports.logout = (0, middleware_1.asyncHandler)(async (req, res) => {
    const user = req.user;
    user.refreshTokens = [];
    await user.save();
    res.status(200).json({
        success: true,
        message: 'Logout successful'
    });
});
//# sourceMappingURL=authController.js.map