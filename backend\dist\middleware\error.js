"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.notFound = exports.errorHandler = exports.CustomError = void 0;
const config_1 = require("../config");
class CustomError extends Error {
    constructor(message, statusCode = 500) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.CustomError = CustomError;
const handleCastError = (error) => {
    const message = `Invalid ${error.path}: ${error.value}`;
    return new CustomError(message, 400);
};
const handleDuplicateKeyError = (error) => {
    const field = Object.keys(error.keyValue)[0];
    const message = `${field} already exists`;
    return new CustomError(message, 400);
};
const handleValidationError = (error) => {
    const errors = Object.values(error.errors).map((err) => err.message);
    const message = `Validation failed: ${errors.join(', ')}`;
    return new CustomError(message, 400);
};
const handleJWTError = () => {
    return new CustomError('Invalid token', 401);
};
const handleJWTExpiredError = () => {
    return new CustomError('Token expired', 401);
};
const sendErrorDev = (err, res) => {
    res.status(err.statusCode || 500).json({
        success: false,
        error: err.message,
        stack: err.stack,
        details: err
    });
};
const sendErrorProd = (err, res) => {
    if (err.isOperational) {
        res.status(err.statusCode || 500).json({
            success: false,
            error: err.message
        });
    }
    else {
        console.error('ERROR:', err);
        res.status(500).json({
            success: false,
            error: 'Something went wrong'
        });
    }
};
const errorHandler = (err, req, res, next) => {
    let error = { ...err };
    error.message = err.message;
    console.error('Error:', err);
    if (err.name === 'CastError') {
        error = handleCastError(err);
    }
    if (err.code === 11000) {
        error = handleDuplicateKeyError(err);
    }
    if (err.name === 'ValidationError') {
        error = handleValidationError(err);
    }
    if (err.name === 'JsonWebTokenError') {
        error = handleJWTError();
    }
    if (err.name === 'TokenExpiredError') {
        error = handleJWTExpiredError();
    }
    if (config_1.config.NODE_ENV === 'development') {
        sendErrorDev(error, res);
    }
    else {
        sendErrorProd(error, res);
    }
};
exports.errorHandler = errorHandler;
const notFound = (req, res, next) => {
    const error = new CustomError(`Route ${req.originalUrl} not found`, 404);
    next(error);
};
exports.notFound = notFound;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=error.js.map