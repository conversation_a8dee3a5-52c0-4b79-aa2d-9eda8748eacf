# Police Reporting System - Mobile App

A Flutter mobile application for police officers to report incidents in the field with offline capabilities.

## 🚀 Features

- **Secure Authentication**: JWT-based login for officers
- **Incident Reporting**: Create detailed incident reports with location and media
- **Offline Mode**: Save reports locally when offline, sync when online
- **Real-time Sync**: Automatic synchronization with backend API
- **Media Upload**: Attach photos and videos to incident reports
- **Location Services**: Automatic GPS location capture
- **Multi-language Support**: English, French, and Arabic
- **Role-based Access**: Different features based on user role

## 📋 Prerequisites

- Flutter 3.16.0 or higher
- Dart 3.0.0 or higher
- Android Studio / VS Code with Flutter extensions
- Android SDK (for Android development)
- Xcode (for iOS development, macOS only)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mobile
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure API endpoint**
   Update the API base URL in `lib/utils/constants.dart`:
   ```dart
   static const String apiBaseUrl = 'http://your-api-server:3000/api';
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

## 📱 App Structure

```
lib/
├── models/          # Data models
├── providers/       # State management (Provider pattern)
├── screens/         # UI screens
│   ├── auth/        # Authentication screens
│   ├── home/        # Home dashboard
│   └── incidents/   # Incident-related screens
├── services/        # API and storage services
├── utils/           # Constants, themes, utilities
└── widgets/         # Reusable UI components
```

## 🔧 Configuration

### API Configuration
Update `lib/utils/constants.dart` with your backend API URL:
```dart
static const String apiBaseUrl = 'http://localhost:3000/api';
```

### Permissions
The app requires the following permissions:
- **Location**: For GPS coordinates in incident reports
- **Camera**: For taking photos and videos
- **Storage**: For saving media files
- **Internet**: For API communication

## 🌐 Offline Capabilities

- Reports are saved locally using SQLite when offline
- Automatic sync when internet connection is restored
- Visual indicators for offline mode and sync status
- Offline reports are clearly marked in the UI

## 🔐 Security Features

- JWT token-based authentication
- Secure storage for sensitive data
- Input validation and sanitization
- Role-based access control

## 📊 State Management

The app uses the Provider pattern for state management:
- **AuthProvider**: User authentication and profile management
- **IncidentProvider**: Incident data and operations
- **ConnectivityProvider**: Network status monitoring

## 🎨 UI/UX Features

- Material Design 3 components
- Dark and light theme support
- Responsive design for different screen sizes
- Intuitive navigation with bottom navigation bar
- Pull-to-refresh functionality

## 🧪 Testing

```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/
```

## 📦 Building for Production

### Android
```bash
flutter build apk --release
# or for app bundle
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## 🔧 Troubleshooting

### Common Issues

1. **Build errors**: Run `flutter clean && flutter pub get`
2. **Location not working**: Check permissions in device settings
3. **API connection issues**: Verify backend URL and network connectivity

### Debug Mode
```bash
flutter run --debug
```

## 📚 Dependencies

Key packages used:
- `provider`: State management
- `http`: API communication
- `shared_preferences`: Local storage
- `flutter_secure_storage`: Secure token storage
- `geolocator`: Location services
- `image_picker`: Camera and gallery access
- `connectivity_plus`: Network status monitoring

## 🤝 Contributing

1. Follow Flutter/Dart coding conventions
2. Add tests for new features
3. Update documentation
4. Ensure offline functionality works properly

## 📄 License

This project is licensed under the MIT License.
