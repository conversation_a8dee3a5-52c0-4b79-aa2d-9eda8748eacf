{"roots": ["police_reporting_app"], "packages": [{"name": "police_reporting_app", "version": "1.0.0+1", "dependencies": ["camera", "connectivity_plus", "cupertino_icons", "dio", "flutter", "flutter_localizations", "flutter_secure_storage", "geocoding", "geolocator", "google_maps_flutter", "http", "image_picker", "intl", "path_provider", "provider", "shared_preferences", "sqflite", "uuid", "video_player"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_lints", "version": "3.0.2", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "flutter_secure_storage", "version": "9.2.4", "dependencies": ["flutter", "flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_platform_interface", "flutter_secure_storage_web", "flutter_secure_storage_windows", "meta"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "connectivity_plus", "version": "5.0.2", "dependencies": ["connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "js", "meta", "nm"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "video_player", "version": "2.10.0", "dependencies": ["flutter", "html", "video_player_android", "video_player_avfoundation", "video_player_platform_interface", "video_player_web"]}, {"name": "camera", "version": "0.10.6", "dependencies": ["camera_android", "camera_avfoundation", "camera_platform_interface", "camera_web", "flutter", "flutter_plugin_android_lifecycle"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "google_maps_flutter", "version": "2.12.3", "dependencies": ["flutter", "google_maps_flutter_android", "google_maps_flutter_ios", "google_maps_flutter_platform_interface", "google_maps_flutter_web"]}, {"name": "geocoding", "version": "2.2.2", "dependencies": ["flutter", "geocoding_android", "geocoding_ios", "geocoding_platform_interface"]}, {"name": "geolocator", "version": "10.1.1", "dependencies": ["flutter", "geolocator_android", "geolocator_apple", "geolocator_platform_interface", "geolocator_web", "geolocator_windows"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "dio", "version": "5.8.0+1", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "path"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["characters", "clock", "collection", "flutter", "intl", "material_color_utilities", "meta", "path", "vector_math"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "lints", "version": "3.0.0", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "flutter_secure_storage_windows", "version": "3.1.2", "dependencies": ["ffi", "flutter", "flutter_secure_storage_platform_interface", "path", "path_provider", "win32"]}, {"name": "flutter_secure_storage_web", "version": "1.2.1", "dependencies": ["flutter", "flutter_secure_storage_platform_interface", "flutter_web_plugins", "js"]}, {"name": "flutter_secure_storage_platform_interface", "version": "1.1.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_secure_storage_macos", "version": "3.1.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "flutter_secure_storage_linux", "version": "1.2.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "connectivity_plus_platform_interface", "version": "1.2.4", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "video_player_web", "version": "2.4.0", "dependencies": ["flutter", "flutter_web_plugins", "video_player_platform_interface", "web"]}, {"name": "video_player_platform_interface", "version": "6.4.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "video_player_avfoundation", "version": "2.8.0", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "video_player_android", "version": "2.8.7", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.28", "dependencies": ["flutter"]}, {"name": "camera_web", "version": "0.3.5", "dependencies": ["camera_platform_interface", "flutter", "flutter_web_plugins", "stream_transform", "web"]}, {"name": "camera_platform_interface", "version": "2.10.0", "dependencies": ["cross_file", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "camera_avfoundation", "version": "0.9.20+5", "dependencies": ["camera_platform_interface", "flutter", "stream_transform"]}, {"name": "camera_android", "version": "0.10.10+4", "dependencies": ["camera_platform_interface", "flutter", "flutter_plugin_android_lifecycle", "stream_transform"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.6", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+24", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "google_maps_flutter_web", "version": "0.5.12+2", "dependencies": ["collection", "flutter", "flutter_web_plugins", "google_maps", "google_maps_flutter_platform_interface", "sanitize_html", "stream_transform", "web"]}, {"name": "google_maps_flutter_platform_interface", "version": "2.12.1", "dependencies": ["collection", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "google_maps_flutter_ios", "version": "2.15.4", "dependencies": ["flutter", "google_maps_flutter_platform_interface", "stream_transform"]}, {"name": "google_maps_flutter_android", "version": "2.17.0", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "google_maps_flutter_platform_interface", "stream_transform"]}, {"name": "geocoding_ios", "version": "2.3.0", "dependencies": ["flutter", "geocoding_platform_interface"]}, {"name": "geocoding_android", "version": "3.3.1", "dependencies": ["flutter", "geocoding_platform_interface"]}, {"name": "geocoding_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "geolocator_windows", "version": "0.2.5", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_web", "version": "2.2.1", "dependencies": ["flutter", "flutter_web_plugins", "geolocator_platform_interface"]}, {"name": "geolocator_apple", "version": "2.3.13", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_android", "version": "4.6.2", "dependencies": ["flutter", "geolocator_platform_interface", "meta", "uuid"]}, {"name": "geolocator_platform_interface", "version": "4.2.6", "dependencies": ["flutter", "meta", "plugin_platform_interface", "vector_math"]}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "file_selector_macos", "version": "0.9.4+3", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "sanitize_html", "version": "2.1.0", "dependencies": ["html", "meta"]}, {"name": "google_maps", "version": "8.1.1", "dependencies": ["meta", "web"]}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}], "configVersion": 1}