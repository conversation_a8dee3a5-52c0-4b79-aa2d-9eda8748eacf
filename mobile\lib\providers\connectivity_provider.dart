import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityProvider with ChangeNotifier {
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;

  bool _isOnline = true;
  bool _wasOffline = false;

  bool get isOnline => _isOnline;
  bool get isOffline => !_isOnline;
  bool get wasOffline => _wasOffline;

  ConnectivityProvider() {
    _initConnectivity();
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  Future<void> _initConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } catch (e) {
      debugPrint('Could not check connectivity status: $e');
    }
  }

  void _updateConnectionStatus(ConnectivityResult result) {
    final wasOnline = _isOnline;
    _isOnline = result != ConnectivityResult.none;

    if (wasOnline && !_isOnline) {
      // Just went offline
      _wasOffline = true;
      debugPrint('📱 Device went offline');
    } else if (!wasOnline && _isOnline) {
      // Just came back online
      debugPrint('📱 Device came back online');
      if (_wasOffline) {
        // Trigger sync if we were offline
        _triggerSync();
      }
    }

    notifyListeners();
  }

  void _triggerSync() {
    // This will be called when device comes back online
    // The UI can listen to this provider and trigger sync
    debugPrint('🔄 Triggering sync after coming back online');
  }

  void markSyncComplete() {
    _wasOffline = false;
    notifyListeners();
  }

  @override
  void dispose() {
    _connectivitySubscription.cancel();
    super.dispose();
  }
}
