import 'package:flutter/material.dart';
import '../models/models.dart';
import '../screens/splash_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/home/<USER>';

class AppConstants {
  // API Configuration
  static const String apiBaseUrl = 'http://localhost:3000/api';
  static const int requestTimeout = 30000; // 30 seconds

  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String languageKey = 'app_language';

  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  static const List<String> allowedVideoTypes = ['mp4', 'mov', 'avi'];

  // Offline Sync
  static const int syncInterval = 30000; // 30 seconds
  static const String offlineDbName = 'police_reporting_offline.db';

  // Map Configuration
  static const double defaultMapZoom = 15.0;
  static const double locationAccuracy = 100.0; // meters

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double defaultRadius = 8.0;
  static const Duration animationDuration = Duration(milliseconds: 300);
}

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF1976D2);
  static const Color primaryDark = Color(0xFF0D47A1);
  static const Color primaryLight = Color(0xFF42A5F5);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Priority Colors
  static const Map<Priority, Color> priorityColors = {
    Priority.low: Color(0xFF4CAF50),
    Priority.medium: Color(0xFFFF9800),
    Priority.high: Color(0xFFFF5722),
    Priority.critical: Color(0xFFF44336),
  };

  // Status Colors
  static const Map<IncidentStatus, Color> statusColors = {
    IncidentStatus.pending: Color(0xFF9E9E9E),
    IncidentStatus.investigating: Color(0xFF2196F3),
    IncidentStatus.resolved: Color(0xFF4CAF50),
    IncidentStatus.closed: Color(0xFF607D8B),
  };

  // Incident Type Colors
  static const Map<IncidentType, Color> incidentTypeColors = {
    IncidentType.crime: Color(0xFFF44336),
    IncidentType.accident: Color(0xFFFF9800),
    IncidentType.threat: Color(0xFFE91E63),
    IncidentType.suspicious: Color(0xFF9C27B0),
    IncidentType.emergency: Color(0xFFF44336),
    IncidentType.traffic: Color(0xFFFF9800),
    IncidentType.domestic: Color(0xFF00BCD4),
    IncidentType.theft: Color(0xFFE91E63),
    IncidentType.assault: Color(0xFFF44336),
    IncidentType.other: Color(0xFF9E9E9E),
  };

  // Neutral Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF212121);
  static const Color onBackground = Color(0xFF212121);
}

class AppStrings {
  // App Info
  static const String appName = 'Police Reporting';
  static const String appVersion = '1.0.0';

  // Common
  static const String ok = 'OK';
  static const String cancel = 'Cancel';
  static const String save = 'Save';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String loading = 'Loading...';
  static const String retry = 'Retry';
  static const String noData = 'No data available';
  static const String error = 'Error';
  static const String success = 'Success';

  // Auth
  static const String login = 'Login';
  static const String logout = 'Logout';
  static const String email = 'Email';
  static const String password = 'Password';
  static const String loginFailed = 'Login failed';
  static const String invalidCredentials = 'Invalid email or password';

  // Incidents
  static const String incidents = 'Incidents';
  static const String newIncident = 'New Incident';
  static const String incidentDetails = 'Incident Details';
  static const String reportIncident = 'Report Incident';
  static const String incidentType = 'Incident Type';
  static const String description = 'Description';
  static const String location = 'Location';
  static const String priority = 'Priority';
  static const String status = 'Status';
  static const String attachments = 'Attachments';

  // Validation
  static const String fieldRequired = 'This field is required';
  static const String invalidEmail = 'Please enter a valid email';
  static const String passwordTooShort =
      'Password must be at least 8 characters';

  // Network
  static const String noInternet = 'No internet connection';
  static const String serverError = 'Server error occurred';
  static const String requestTimeout = 'Request timeout';

  // Offline
  static const String offlineMode = 'Offline Mode';
  static const String syncPending = 'Sync Pending';
  static const String syncComplete = 'Sync Complete';
  static const String syncFailed = 'Sync Failed';
}

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String home = '/home';
  static const String incidentList = '/incidents';
  static const String incidentDetails = '/incident-details';
  static const String newIncident = '/new-incident';
  static const String profile = '/profile';
  static const String settings = '/settings';

  static Map<String, WidgetBuilder> get routes => {
        splash: (context) => const SplashScreen(),
        login: (context) => const LoginScreen(),
        home: (context) => const HomeScreen(),
        // Other routes will be added as screens are created
      };

  static Route<dynamic>? generateRoute(RouteSettings settings) {
    // Dynamic route generation for parameterized routes
    switch (settings.name) {
      case incidentDetails:
        final incidentId = settings.arguments as String?;
        if (incidentId != null) {
          // Return incident details screen with ID
          return MaterialPageRoute(
            builder: (context) => Scaffold(
              appBar: AppBar(title: const Text('Incident Details')),
              body: Center(child: Text('Incident ID: $incidentId')),
            ),
          );
        }
        break;
    }
    return null;
  }
}
