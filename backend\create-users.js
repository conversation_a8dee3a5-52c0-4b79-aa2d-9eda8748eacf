// Quick script to create test users
const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function createUsers() {
  try {
    // Create admin user
    console.log('Creating admin user...');
    const adminResponse = await axios.post(`${API_BASE}/auth/register`, {
      email: '<EMAIL>',
      password: 'admin123456',
      firstName: 'System',
      lastName: 'Administrator',
      role: 'admin',
      badgeNumber: 'ADMIN001'
    });
    console.log('✅ Admin user created:', adminResponse.data);

    // Create officer user
    console.log('Creating officer user...');
    const officerResponse = await axios.post(`${API_BASE}/auth/register`, {
      email: '<EMAIL>',
      password: 'officer123',
      firstName: 'John',
      lastName: 'Smith',
      role: 'officer',
      unit: 'Patrol Unit A',
      badgeNumber: 'OFF001'
    });
    console.log('✅ Officer user created:', officerResponse.data);

    // Create supervisor user
    console.log('Creating supervisor user...');
    const supervisorResponse = await axios.post(`${API_BASE}/auth/register`, {
      email: '<EMAIL>',
      password: 'super123',
      firstName: 'Jane',
      lastName: 'Doe',
      role: 'supervisor',
      unit: 'Operations',
      badgeNumber: 'SUP001'
    });
    console.log('✅ Supervisor user created:', supervisorResponse.data);

  } catch (error) {
    if (error.response) {
      console.error('❌ Error:', error.response.data);
    } else {
      console.error('❌ Error:', error.message);
    }
  }
}

createUsers();
