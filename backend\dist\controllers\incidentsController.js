"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.assignIncident = exports.getIncidentStats = exports.deleteIncident = exports.updateIncident = exports.getIncidentById = exports.getIncidents = exports.createIncident = void 0;
const models_1 = require("../models");
const middleware_1 = require("../middleware");
const types_1 = require("../shared/types");
exports.createIncident = (0, middleware_1.asyncHandler)(async (req, res) => {
    const user = req.user;
    const incidentData = req.body;
    const incident = await models_1.Incident.create({
        ...incidentData,
        reportedBy: user._id,
        reportedAt: new Date()
    });
    await incident.populate('reportedBy', 'firstName lastName email badgeNumber');
    res.status(201).json({
        success: true,
        data: incident,
        message: 'Incident created successfully'
    });
});
exports.getIncidents = (0, middleware_1.asyncHandler)(async (req, res) => {
    const { type, status, priority, region, dateFrom, dateTo, reportedBy, search, page = 1, limit = 20 } = req.query;
    const filter = {};
    if (type) {
        filter.type = Array.isArray(type) ? { $in: type } : type;
    }
    if (status) {
        filter.status = Array.isArray(status) ? { $in: status } : status;
    }
    if (priority) {
        filter.priority = Array.isArray(priority) ? { $in: priority } : priority;
    }
    if (region) {
        filter['location.region'] = Array.isArray(region) ? { $in: region } : region;
    }
    if (reportedBy) {
        filter.reportedBy = Array.isArray(reportedBy) ? { $in: reportedBy } : reportedBy;
    }
    if (dateFrom || dateTo) {
        filter.reportedAt = {};
        if (dateFrom)
            filter.reportedAt.$gte = new Date(dateFrom);
        if (dateTo)
            filter.reportedAt.$lte = new Date(dateTo);
    }
    if (search) {
        filter.$text = { $search: search };
    }
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;
    const [incidents, total] = await Promise.all([
        models_1.Incident.find(filter)
            .populate('reportedBy', 'firstName lastName email badgeNumber')
            .populate('assignedTo', 'firstName lastName email')
            .sort({ reportedAt: -1 })
            .skip(skip)
            .limit(limitNum),
        models_1.Incident.countDocuments(filter)
    ]);
    const response = {
        data: incidents,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(total / limitNum)
    };
    res.status(200).json({
        success: true,
        data: response
    });
});
exports.getIncidentById = (0, middleware_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const incident = await models_1.Incident.findById(id)
        .populate('reportedBy', 'firstName lastName email badgeNumber unit')
        .populate('assignedTo', 'firstName lastName email');
    if (!incident) {
        throw new middleware_1.CustomError('Incident not found', 404);
    }
    res.status(200).json({
        success: true,
        data: incident
    });
});
exports.updateIncident = (0, middleware_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const user = req.user;
    const incident = await models_1.Incident.findById(id);
    if (!incident) {
        throw new middleware_1.CustomError('Incident not found', 404);
    }
    const canUpdate = incident.reportedBy.toString() === user._id.toString() ||
        incident.assignedTo?.toString() === user._id.toString() ||
        ['supervisor', 'admin'].includes(user.role);
    if (!canUpdate) {
        throw new middleware_1.CustomError('Not authorized to update this incident', 403);
    }
    Object.assign(incident, updateData);
    if (updateData.status === types_1.IncidentStatus.RESOLVED && incident.status !== types_1.IncidentStatus.RESOLVED) {
        incident.resolvedAt = new Date();
    }
    await incident.save();
    await incident.populate('reportedBy', 'firstName lastName email badgeNumber');
    await incident.populate('assignedTo', 'firstName lastName email');
    res.status(200).json({
        success: true,
        data: incident,
        message: 'Incident updated successfully'
    });
});
exports.deleteIncident = (0, middleware_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const incident = await models_1.Incident.findById(id);
    if (!incident) {
        throw new middleware_1.CustomError('Incident not found', 404);
    }
    await models_1.Incident.findByIdAndDelete(id);
    res.status(200).json({
        success: true,
        message: 'Incident deleted successfully'
    });
});
exports.getIncidentStats = (0, middleware_1.asyncHandler)(async (req, res) => {
    const { dateFrom, dateTo, region } = req.query;
    const matchFilter = {};
    if (dateFrom || dateTo) {
        matchFilter.reportedAt = {};
        if (dateFrom)
            matchFilter.reportedAt.$gte = new Date(dateFrom);
        if (dateTo)
            matchFilter.reportedAt.$lte = new Date(dateTo);
    }
    if (region) {
        matchFilter['location.region'] = region;
    }
    const [totalIncidents, byType, byStatus, byPriority, byRegion, recentTrends] = await Promise.all([
        models_1.Incident.countDocuments(matchFilter),
        models_1.Incident.aggregate([
            { $match: matchFilter },
            { $group: { _id: '$type', count: { $sum: 1 } } }
        ]),
        models_1.Incident.aggregate([
            { $match: matchFilter },
            { $group: { _id: '$status', count: { $sum: 1 } } }
        ]),
        models_1.Incident.aggregate([
            { $match: matchFilter },
            { $group: { _id: '$priority', count: { $sum: 1 } } }
        ]),
        models_1.Incident.aggregate([
            { $match: matchFilter },
            { $group: { _id: '$location.region', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 10 }
        ]),
        models_1.Incident.aggregate([
            {
                $match: {
                    ...matchFilter,
                    reportedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
                }
            },
            {
                $group: {
                    _id: {
                        $dateToString: { format: '%Y-%m-%d', date: '$reportedAt' }
                    },
                    count: { $sum: 1 }
                }
            },
            { $sort: { _id: 1 } }
        ])
    ]);
    const stats = {
        totalIncidents,
        byType: Object.values(types_1.IncidentType).reduce((acc, type) => {
            acc[type] = byType.find(item => item._id === type)?.count || 0;
            return acc;
        }, {}),
        byStatus: Object.values(types_1.IncidentStatus).reduce((acc, status) => {
            acc[status] = byStatus.find(item => item._id === status)?.count || 0;
            return acc;
        }, {}),
        byPriority: Object.values(types_1.Priority).reduce((acc, priority) => {
            acc[priority] = byPriority.find(item => item._id === priority)?.count || 0;
            return acc;
        }, {}),
        byRegion: byRegion.reduce((acc, item) => {
            if (item._id)
                acc[item._id] = item.count;
            return acc;
        }, {}),
        recentTrends: recentTrends.map(item => ({
            date: item._id,
            count: item.count
        }))
    };
    res.status(200).json({
        success: true,
        data: stats
    });
});
exports.assignIncident = (0, middleware_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { assignedTo } = req.body;
    const incident = await models_1.Incident.findById(id);
    if (!incident) {
        throw new middleware_1.CustomError('Incident not found', 404);
    }
    incident.assignedTo = assignedTo;
    incident.status = types_1.IncidentStatus.INVESTIGATING;
    await incident.save();
    await incident.populate('assignedTo', 'firstName lastName email');
    res.status(200).json({
        success: true,
        data: incident,
        message: 'Incident assigned successfully'
    });
});
//# sourceMappingURL=incidentsController.js.map