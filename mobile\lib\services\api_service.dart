import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/models.dart';
import '../utils/constants.dart';
import 'storage_service.dart';

class ApiService {
  static const String baseUrl = AppConstants.apiBaseUrl;
  final StorageService _storageService = StorageService();

  // Get headers with authentication
  Future<Map<String, String>> _getHeaders({bool includeAuth = true}) async {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (includeAuth) {
      final token = await _storageService.getToken();
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  // Handle API response
  ApiResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(dynamic)? fromJson,
  ) {
    final Map<String, dynamic> data = json.decode(response.body);

    if (response.statusCode >= 200 && response.statusCode < 300) {
      return ApiResponse<T>.fromJson(data, fromJson);
    } else {
      return ApiResponse<T>(
        success: false,
        error: data['error'] ?? 'Unknown error occurred',
      );
    }
  }

  // Authentication endpoints
  Future<ApiResponse<AuthResponse>> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/login'),
        headers: await _getHeaders(includeAuth: false),
        body: json.encode({'email': email, 'password': password}),
      );

      return _handleResponse<AuthResponse>(
        response,
        (data) => AuthResponse.fromJson(data),
      );
    } catch (e) {
      return ApiResponse<AuthResponse>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  Future<ApiResponse<User>> getProfile() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/auth/profile'),
        headers: await _getHeaders(),
      );

      return _handleResponse<User>(response, (data) => User.fromJson(data));
    } catch (e) {
      return ApiResponse<User>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  Future<ApiResponse<User>> updateProfile(Map<String, dynamic> data) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/auth/profile'),
        headers: await _getHeaders(),
        body: json.encode(data),
      );

      return _handleResponse<User>(response, (data) => User.fromJson(data));
    } catch (e) {
      return ApiResponse<User>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  Future<ApiResponse<void>> logout() async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/logout'),
        headers: await _getHeaders(),
      );

      return _handleResponse<void>(response, null);
    } catch (e) {
      return ApiResponse<void>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  // Incident endpoints
  Future<ApiResponse<List<Incident>>> getIncidents({
    Map<String, dynamic>? filters,
  }) async {
    try {
      String url = '$baseUrl/incidents';

      if (filters != null && filters.isNotEmpty) {
        final queryParams = <String>[];
        filters.forEach((key, value) {
          if (value != null) {
            queryParams.add('$key=${Uri.encodeComponent(value.toString())}');
          }
        });
        if (queryParams.isNotEmpty) {
          url += '?${queryParams.join('&')}';
        }
      }

      final response = await http.get(
        Uri.parse(url),
        headers: await _getHeaders(),
      );

      return _handleResponse<List<Incident>>(response, (data) {
        final incidents = data['data'] as List;
        return incidents.map((i) => Incident.fromJson(i)).toList();
      });
    } catch (e) {
      return ApiResponse<List<Incident>>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  Future<ApiResponse<Incident>> createIncident(
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/incidents'),
        headers: await _getHeaders(),
        body: json.encode(data),
      );

      return _handleResponse<Incident>(
        response,
        (data) => Incident.fromJson(data),
      );
    } catch (e) {
      return ApiResponse<Incident>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  Future<ApiResponse<Incident>> getIncident(String id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/incidents/$id'),
        headers: await _getHeaders(),
      );

      return _handleResponse<Incident>(
        response,
        (data) => Incident.fromJson(data),
      );
    } catch (e) {
      return ApiResponse<Incident>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  Future<ApiResponse<Incident>> updateIncident(
    String id,
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/incidents/$id'),
        headers: await _getHeaders(),
        body: json.encode(data),
      );

      return _handleResponse<Incident>(
        response,
        (data) => Incident.fromJson(data),
      );
    } catch (e) {
      return ApiResponse<Incident>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  // File upload
  Future<ApiResponse<String>> uploadFile(File file) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/media/upload'),
      );

      final token = await _storageService.getToken();
      if (token != null) {
        request.headers['Authorization'] = 'Bearer $token';
      }

      request.files.add(await http.MultipartFile.fromPath('file', file.path));

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      return _handleResponse<String>(response, (data) => data['url'] as String);
    } catch (e) {
      return ApiResponse<String>(
        success: false,
        error: 'Upload error: ${e.toString()}',
      );
    }
  }
}
