import { Router } from 'express';
import {
  login,
  register,
  getProfile,
  updateProfile,
  changePassword,
  logout
} from '../controllers';
import {
  authenticate,
  requireAdmin,
  validateLogin,
  validateUserRegistration
} from '../middleware';
import { body } from 'express-validator';

const router = Router();

// Public routes
router.post('/login', validateLogin, login);

// Protected routes
router.use(authenticate); // All routes below require authentication

router.get('/profile', getProfile);
router.put('/profile', [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('unit')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Unit name cannot exceed 100 characters')
], updateProfile);

router.put('/change-password', [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number')
], changePassword);

router.post('/logout', logout);

// Admin only routes (temporarily open for testing)
router.post('/register', validateUserRegistration, register);

export default router;
