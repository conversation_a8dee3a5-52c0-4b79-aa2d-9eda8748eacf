import { Request, Response } from 'express';
import jwt, { SignOptions } from 'jsonwebtoken';
import { User } from '../models';
import { config } from '../config';
import { asyncHandler, CustomError } from '../middleware';
import { AuthResponse, LoginRequest } from '../shared/types';

/**
 * Generate JWT token
 */
const generateToken = (userId: string, email: string, role: string): string => {
  return jwt.sign(
    { userId, email, role },
    config.JWT_SECRET,
    { expiresIn: config.JWT_EXPIRES_IN as any }
  );
};

/**
 * Generate refresh token
 */
const generateRefreshToken = (userId: string): string => {
  return jwt.sign(
    { userId },
    config.JWT_REFRESH_SECRET,
    { expiresIn: config.JWT_REFRESH_EXPIRES_IN as any }
  );
};

/**
 * @desc    Login user
 * @route   POST /api/auth/login
 * @access  Public
 */
export const login = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { email, password }: LoginRequest = req.body;

  // Find user and include password for comparison
  const user = await User.findOne({ email }).select('+password');

  if (!user || !user.isActive) {
    throw new CustomError('Invalid credentials', 401);
  }

  // Check password
  const isPasswordValid = await user.comparePassword(password);
  if (!isPasswordValid) {
    throw new CustomError('Invalid credentials', 401);
  }

  // Update last login
  user.lastLogin = new Date();
  await user.save();

  // Generate tokens
  const token = generateToken((user._id as string).toString(), user.email, user.role);
  const refreshToken = generateRefreshToken((user._id as string).toString());

  // Add refresh token to user
  user.generateRefreshToken();
  await user.save();

  const response: AuthResponse = {
    token,
    user: {
      id: (user._id as string).toString(),
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      unit: user.unit,
      badgeNumber: user.badgeNumber,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    },
    expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds
  };

  res.status(200).json({
    success: true,
    data: response,
    message: 'Login successful'
  });
});

/**
 * @desc    Register new user (Admin only)
 * @route   POST /api/auth/register
 * @access  Private (Admin)
 */
export const register = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { email, password, firstName, lastName, role, unit, badgeNumber } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({ email });
  if (existingUser) {
    throw new CustomError('User already exists with this email', 400);
  }

  // Check if badge number already exists (if provided)
  if (badgeNumber) {
    const existingBadge = await User.findOne({ badgeNumber });
    if (existingBadge) {
      throw new CustomError('Badge number already exists', 400);
    }
  }

  // Create user
  const user = await User.create({
    email,
    password,
    firstName,
    lastName,
    role,
    unit,
    badgeNumber
  });

  res.status(201).json({
    success: true,
    data: user.toJSON(),
    message: 'User registered successfully'
  });
});

/**
 * @desc    Get current user profile
 * @route   GET /api/auth/profile
 * @access  Private
 */
export const getProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const user = req.user!;

  res.status(200).json({
    success: true,
    data: user.toJSON()
  });
});

/**
 * @desc    Update user profile
 * @route   PUT /api/auth/profile
 * @access  Private
 */
export const updateProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const user = req.user!;
  const { firstName, lastName, unit } = req.body;

  // Update allowed fields
  if (firstName) user.firstName = firstName;
  if (lastName) user.lastName = lastName;
  if (unit !== undefined) user.unit = unit;

  await user.save();

  res.status(200).json({
    success: true,
    data: user.toJSON(),
    message: 'Profile updated successfully'
  });
});

/**
 * @desc    Change password
 * @route   PUT /api/auth/change-password
 * @access  Private
 */
export const changePassword = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const user = await User.findById(req.user!._id).select('+password');
  const { currentPassword, newPassword } = req.body;

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  // Verify current password
  const isCurrentPasswordValid = await user.comparePassword(currentPassword);
  if (!isCurrentPasswordValid) {
    throw new CustomError('Current password is incorrect', 400);
  }

  // Update password
  user.password = newPassword;
  await user.save();

  res.status(200).json({
    success: true,
    message: 'Password changed successfully'
  });
});

/**
 * @desc    Logout user
 * @route   POST /api/auth/logout
 * @access  Private
 */
export const logout = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const user = req.user!;

  // Clear refresh tokens
  user.refreshTokens = [];
  await user.save();

  res.status(200).json({
    success: true,
    message: 'Logout successful'
  });
});
