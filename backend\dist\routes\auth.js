"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const middleware_1 = require("../middleware");
const express_validator_1 = require("express-validator");
const router = (0, express_1.Router)();
router.post('/login', middleware_1.validateLogin, controllers_1.login);
router.use(middleware_1.authenticate);
router.get('/profile', controllers_1.getProfile);
router.put('/profile', [
    (0, express_validator_1.body)('firstName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('lastName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('unit')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Unit name cannot exceed 100 characters')
], controllers_1.updateProfile);
router.put('/change-password', [
    (0, express_validator_1.body)('currentPassword')
        .notEmpty()
        .withMessage('Current password is required'),
    (0, express_validator_1.body)('newPassword')
        .isLength({ min: 8 })
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number')
], controllers_1.changePassword);
router.post('/logout', controllers_1.logout);
router.post('/register', middleware_1.validateUserRegistration, controllers_1.register);
exports.default = router;
//# sourceMappingURL=auth.js.map