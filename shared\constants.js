"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.COLORS = exports.SUCCESS_MESSAGES = exports.ERROR_MESSAGES = exports.VALIDATION_RULES = exports.ROLE_LABELS = exports.STATUS_LABELS = exports.PRIORITY_LABELS = exports.INCIDENT_TYPE_LABELS = exports.APP_CONFIG = exports.API_ENDPOINTS = void 0;
exports.API_ENDPOINTS = {
    AUTH: {
        LOGIN: '/api/auth/login',
        LOGOUT: '/api/auth/logout',
        REFRESH: '/api/auth/refresh',
        PROFILE: '/api/auth/profile'
    },
    INCIDENTS: {
        BASE: '/api/incidents',
        STATS: '/api/incidents/stats',
        EXPORT: '/api/incidents/export'
    },
    USERS: {
        BASE: '/api/users',
        PROFILE: '/api/users/profile'
    },
    MEDIA: {
        UPLOAD: '/api/media/upload',
        BASE: '/api/media'
    },
    ALERTS: '/api/alerts'
};
exports.APP_CONFIG = {
    MAX_FILE_SIZE: 10 * 1024 * 1024,
    ALLOWED_FILE_TYPES: [
        'image/jpeg',
        'image/png',
        'image/webp',
        'video/mp4',
        'video/webm',
        'video/quicktime'
    ],
    SUPPORTED_LANGUAGES: ['en', 'fr', 'ar'],
    DEFAULT_LANGUAGE: 'en',
    OFFLINE_SYNC_INTERVAL: 30000,
    MAP_DEFAULT_ZOOM: 13,
    PAGINATION_LIMIT: 20
};
exports.INCIDENT_TYPE_LABELS = {
    crime: { en: 'Crime', fr: 'Crime', ar: 'جريمة' },
    accident: { en: 'Accident', fr: 'Accident', ar: 'حادث' },
    threat: { en: 'Threat', fr: 'Menace', ar: 'تهديد' },
    suspicious: { en: 'Suspicious Activity', fr: 'Activité Suspecte', ar: 'نشاط مشبوه' },
    emergency: { en: 'Emergency', fr: 'Urgence', ar: 'طوارئ' },
    traffic: { en: 'Traffic Violation', fr: 'Infraction Routière', ar: 'مخالفة مرورية' },
    domestic: { en: 'Domestic Issue', fr: 'Problème Domestique', ar: 'مشكلة منزلية' },
    theft: { en: 'Theft', fr: 'Vol', ar: 'سرقة' },
    assault: { en: 'Assault', fr: 'Agression', ar: 'اعتداء' },
    other: { en: 'Other', fr: 'Autre', ar: 'أخرى' }
};
exports.PRIORITY_LABELS = {
    low: { en: 'Low', fr: 'Faible', ar: 'منخفض' },
    medium: { en: 'Medium', fr: 'Moyen', ar: 'متوسط' },
    high: { en: 'High', fr: 'Élevé', ar: 'عالي' },
    critical: { en: 'Critical', fr: 'Critique', ar: 'حرج' }
};
exports.STATUS_LABELS = {
    pending: { en: 'Pending', fr: 'En Attente', ar: 'في الانتظار' },
    investigating: { en: 'Investigating', fr: 'En Cours d\'Enquête', ar: 'قيد التحقيق' },
    resolved: { en: 'Resolved', fr: 'Résolu', ar: 'محلول' },
    closed: { en: 'Closed', fr: 'Fermé', ar: 'مغلق' }
};
exports.ROLE_LABELS = {
    officer: { en: 'Officer', fr: 'Officier', ar: 'ضابط' },
    supervisor: { en: 'Supervisor', fr: 'Superviseur', ar: 'مشرف' },
    analyst: { en: 'Analyst', fr: 'Analyste', ar: 'محلل' },
    admin: { en: 'Administrator', fr: 'Administrateur', ar: 'مدير' }
};
exports.VALIDATION_RULES = {
    PASSWORD_MIN_LENGTH: 8,
    TITLE_MAX_LENGTH: 100,
    DESCRIPTION_MAX_LENGTH: 2000,
    TAG_MAX_LENGTH: 50,
    MAX_TAGS: 10,
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PHONE_REGEX: /^\+?[\d\s\-\(\)]+$/
};
exports.ERROR_MESSAGES = {
    UNAUTHORIZED: { en: 'Unauthorized access', fr: 'Accès non autorisé', ar: 'وصول غير مصرح' },
    FORBIDDEN: { en: 'Access forbidden', fr: 'Accès interdit', ar: 'الوصول محظور' },
    NOT_FOUND: { en: 'Resource not found', fr: 'Ressource non trouvée', ar: 'المورد غير موجود' },
    VALIDATION_ERROR: { en: 'Validation error', fr: 'Erreur de validation', ar: 'خطأ في التحقق' },
    SERVER_ERROR: { en: 'Internal server error', fr: 'Erreur interne du serveur', ar: 'خطأ داخلي في الخادم' },
    NETWORK_ERROR: { en: 'Network error', fr: 'Erreur réseau', ar: 'خطأ في الشبكة' },
    FILE_TOO_LARGE: { en: 'File too large', fr: 'Fichier trop volumineux', ar: 'الملف كبير جداً' },
    INVALID_FILE_TYPE: { en: 'Invalid file type', fr: 'Type de fichier invalide', ar: 'نوع ملف غير صالح' }
};
exports.SUCCESS_MESSAGES = {
    INCIDENT_CREATED: { en: 'Incident reported successfully', fr: 'Incident signalé avec succès', ar: 'تم الإبلاغ عن الحادث بنجاح' },
    INCIDENT_UPDATED: { en: 'Incident updated successfully', fr: 'Incident mis à jour avec succès', ar: 'تم تحديث الحادث بنجاح' },
    LOGIN_SUCCESS: { en: 'Login successful', fr: 'Connexion réussie', ar: 'تم تسجيل الدخول بنجاح' },
    LOGOUT_SUCCESS: { en: 'Logout successful', fr: 'Déconnexion réussie', ar: 'تم تسجيل الخروج بنجاح' },
    DATA_SYNCED: { en: 'Data synchronized', fr: 'Données synchronisées', ar: 'تم مزامنة البيانات' }
};
exports.COLORS = {
    PRIORITY: {
        low: '#10B981',
        medium: '#F59E0B',
        high: '#EF4444',
        critical: '#DC2626'
    },
    STATUS: {
        pending: '#6B7280',
        investigating: '#3B82F6',
        resolved: '#10B981',
        closed: '#374151'
    },
    INCIDENT_TYPE: {
        crime: '#DC2626',
        accident: '#F59E0B',
        threat: '#EF4444',
        suspicious: '#8B5CF6',
        emergency: '#DC2626',
        traffic: '#F59E0B',
        domestic: '#06B6D4',
        theft: '#EC4899',
        assault: '#DC2626',
        other: '#6B7280'
    }
};
//# sourceMappingURL=constants.js.map