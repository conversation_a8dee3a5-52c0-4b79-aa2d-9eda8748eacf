"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = __importDefault(require("./auth"));
const incidents_1 = __importDefault(require("./incidents"));
const router = (0, express_1.Router)();
router.use('/auth', auth_1.default);
router.use('/incidents', incidents_1.default);
router.get('/health', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Police Reporting System API is running',
        timestamp: new Date().toISOString()
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map